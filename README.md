# Unix Timestamp Converter - 多语言时间戳转换工具

一个功能强大的多语言Unix时间戳转换工具，支持10种语言，提供直观的用户界面和丰富的功能特性。

## 🌟 最新特性

### 📋 智能复制系统
- **图标化复制按钮**：使用直观的📋图标替代文字
- **双击快捷复制**：PC端和移动端均支持双击显示快捷复制菜单
- **绿色成功反馈**：复制成功时显示绿色✓动画效果
- **防重复触发**：优化双击逻辑，第一次点击不会触发复制

### 🎯 快捷时间复制
- **预设时间间隔**：1分钟、3分钟、5分钟后的时间戳
- **自定义时间**：支持输入任意分钟数，自动缓存用户偏好
- **实时计算**：动态计算未来时间戳，支持秒级和毫秒级

### 🌍 多语言支持
- **10种语言**：英语、中文、印地语、日语、德语、英式英语、俄语、韩语、加拿大英语、法语
- **智能提示**：PC端显示"双击复制按钮快捷复制"，移动端同样支持双击操作
- **URL路径支持**：支持多语言URL路径访问（如 `/zh-CN/`, `/ja-JP/`）

### 📱 移动端优化
- **触摸友好**：优化移动端触摸体验，支持双击操作
- **响应式设计**：完美适配各种屏幕尺寸
- **华为浏览器兼容**：特别优化华为手机浏览器的复制功能

### 🔧 技术特性
- **多层复制降级**：现代Clipboard API → execCommand → 手动复制对话框
- **智能格式检测**：自动识别秒、毫秒、微秒、纳秒时间戳格式
- **实时转换**：输入即转换，无需点击按钮

---

## 🚀 Google SEO 多语言优化方案

本项目为多语言 Unix 时间戳转换工具，支持10种语言。为通过 Google Adsense 收录审核并获得最佳 SEO 效果，建议按如下细化方案优化：

---

## 1. 多语言SEO核心原则

- **每种语言单独URL**：如 `/`、`/zh-CN/`、`/fr-FR/` 等，便于 Google 抓取和索引。
- **每种语言页面均有独立 `<title>`、`<meta name="description">`、`<meta name="keywords">`**，内容用对应语言填写。
- **使用 `<link rel="alternate" hreflang="xx">` 标签**，帮助 Google 识别各语言版本。
- **允许用户手动切换语言，并确保每种语言页面都能直接访问。**
- **页面内容丰富，避免空壳页面。**

---

## 2. 具体实现细则

### (A) index.html 优化

1. **为每种语言添加 `<link rel="alternate" hreflang>`**
   ```html
   <link rel="alternate" hreflang="en" href="https://unixtime.help/" />
   <link rel="alternate" hreflang="zh-CN" href="https://unixtime.help/zh-CN/" />
   <link rel="alternate" hreflang="hi-IN" href="https://unixtime.help/hi-IN/" />
   <link rel="alternate" hreflang="ja-JP" href="https://unixtime.help/ja-JP/" />
   <link rel="alternate" hreflang="de-DE" href="https://unixtime.help/de-DE/" />
   <link rel="alternate" hreflang="en-GB" href="https://unixtime.help/en-GB/" />
   <link rel="alternate" hreflang="ru-RU" href="https://unixtime.help/ru-RU/" />
   <link rel="alternate" hreflang="ko-KR" href="https://unixtime.help/ko-KR/" />
   <link rel="alternate" hreflang="en-CA" href="https://unixtime.help/en-CA/" />
   <link rel="alternate" hreflang="fr-FR" href="https://unixtime.help/fr-FR/" />
   ```

2. **SEO标签动态切换**
   - translations 变量中为每种语言增加 `seoTitle`、`seoDescription`、`seoKeywords` 字段。
   - 切换语言时，自动同步 `<title>` 和 `<meta>` 内容。
   - 示例：
     ```js
     function updateSEOTags() {
         const lang = translations[currentLang];
         document.title = lang.seoTitle;
         document.querySelector('meta[name="description"]').setAttribute('content', lang.seoDescription);
         document.querySelector('meta[name="keywords"]').setAttribute('content', lang.seoKeywords);
     }
     ```
   - 在 `updateUIText()` 和页面初始化时调用 `updateSEOTags()`。

3. **允许手动切换语言**
   - 页面提供语言切换按钮。
   - 切换后 URL 跳转到对应语言路径（如 `/fr-FR/`）。

### (B) 其它SEO建议

- **内容丰富**：每种语言页面都应有完整、自然的本地化内容。
- **移动端友好**：保证页面自适应，加载速度快。
- **robots.txt**：不要屏蔽任何语言页面。
- **sitemap.xml**：包含所有语言版本的URL，方便Google发现。
- **404页面**：为不存在的语言或页面返回合适的404响应。

---

## 3. 参考代码片段

#### translations 结构示例
```js
const translations = {
  'en-US': {
    seoTitle: 'Unix Timestamp Converter - Free Online Tool for Developers',
    seoDescription: 'Free online Unix timestamp converter. Convert between Unix timestamps and human-readable dates. Supports seconds, milliseconds, microseconds, and nanoseconds.',
    seoKeywords: 'Unix timestamp, timestamp converter, epoch converter, date converter, developer tools',
    // ... 其它原有字段 ...
  },
  'zh-CN': {
    seoTitle: 'Unix 时间戳转换器 - 免费在线工具',
    seoDescription: '免费在线Unix时间戳转换工具，支持秒、毫秒、微秒和纳秒的转换，适合开发者和系统管理员。',
    seoKeywords: 'Unix时间戳, 时间戳转换, epoch转换, 日期转换, 开发者工具',
    // ...
  },
  // 其它多语言 ...
}
```

#### index.html 头部示例
```html
<head>
  <title>Unix Timestamp Converter - Free Online Tool for Developers</title>
  <meta name="description" content="Free online Unix timestamp converter. Convert between Unix timestamps and human-readable dates. Supports seconds, milliseconds, microseconds, and nanoseconds.">
  <meta name="keywords" content="Unix timestamp, timestamp converter, epoch converter, date converter, developer tools">
  <!-- 多语言 hreflang -->
  <link rel="alternate" hreflang="en" href="https://unixtime.help/" />
  <link rel="alternate" hreflang="zh-CN" href="https://unixtime.help/zh-CN/" />
  <!-- ... -->
</head>
```

#### sitemap.xml 示例
```xml
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>https://unixtime.help/</loc>
    <xhtml:link rel="alternate" hreflang="en" href="https://unixtime.help/" />
    <xhtml:link rel="alternate" hreflang="zh-CN" href="https://unixtime.help/zh-CN/" />
    <!-- ... -->
  </url>
  <!-- 其它语言页面 ... -->
</urlset>
```

---

## 4. 总结

- 保证每种语言页面都能被Google单独抓取和索引。
- 每种语言页面均有独立SEO标签和内容。
- 使用 hreflang 和 sitemap.xml 提升多语言SEO效果。
- 内容本地化、页面友好、加载快速。

如需自动化生成多语言页面、SEO标签或 sitemap.xml，可进一步开发脚本或使用静态站点生成器。

---

## 💻 技术栈

- **前端**：原生 HTML5 + CSS3 + JavaScript (ES6+)
- **部署**：Vercel 静态托管
- **多语言**：客户端动态切换，支持URL路径识别
- **响应式**：CSS媒体查询，适配PC和移动端
- **兼容性**：支持现代浏览器，特别优化移动端体验

## 🎮 使用方法

### 基本功能
1. **查看当前时间戳**：页面自动显示当前Unix时间戳（秒和毫秒）
2. **复制时间戳**：点击📋按钮复制当前时间戳
3. **快捷复制**：双击📋按钮打开快捷菜单，选择未来时间

### 时间戳转换
1. **输入时间戳**：在"Timestamp Conversion"区域输入任意时间戳
2. **自动识别格式**：系统自动识别秒/毫秒/微秒/纳秒格式
3. **查看转换结果**：显示GMT时间、本地时间和相对时间

### 日期转换
1. **输入日期**：在"Date Conversion"区域输入日期时间
2. **格式支持**：支持多种日期格式输入
3. **获取时间戳**：自动转换为对应的Unix时间戳

### 语言切换
1. **点击语言按钮**：页面右上角的语言选择器
2. **选择语言**：支持10种语言切换
3. **URL访问**：可直接访问特定语言路径（如 `/zh-CN/`）

## 🌐 在线访问

- **主站**：[https://unixtime.help](https://unixtime.help)
- **中文版**：[https://unixtime.help/zh-CN/](https://unixtime.help/zh-CN/)
- **日文版**：[https://unixtime.help/ja-JP/](https://unixtime.help/ja-JP/)
- **其他语言**：支持 `/hi-IN/`, `/de-DE/`, `/en-GB/`, `/ru-RU/`, `/ko-KR/`, `/en-CA/`, `/fr-FR/`

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目！

---

**开发者工具 | Developer Tools | 开発者ツール | Outils de développeur**
