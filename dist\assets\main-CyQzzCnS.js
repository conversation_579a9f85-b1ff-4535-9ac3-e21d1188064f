import"./modulepreload-polyfill-B5Qt9EMX.js";/* empty css              */const g={"en-US":{seoTitle:"Unix Timestamp Converter - Free Online Tool for Developers",seoDescription:"Free online Unix timestamp converter. Convert between Unix timestamps and human-readable dates. Supports seconds, milliseconds, microseconds, and nanoseconds.",seoKeywords:"Unix timestamp, timestamp converter, epoch converter, date converter, developer tools",pageTitle:"Unix Timestamp Converter",currentSecondsLabel:"Current Timestamp (s)",currentMillisecondsLabel:"Current Timestamp (ms)",currentTimeLabel:"Current Time",copyBtn:"Copy",copied:"Copied",timestampConversionTitle:"Timestamp Conversion",timestampConversionDesc:"Supports seconds, milliseconds, microseconds and nanoseconds",timestampInputPlaceholder:"Enter timestamp",formatLabel:"Format",gmtLabel:"GMT",localTimezoneLabel:"Local Timezone",relativeTimeLabel:"Relative Time",dateConversionTitle:"Date Conversion",dateInputPlaceholder:"YYYY-MM-DD HH:mm:ss",secondsLabel:"Timestamp (s)",millisecondsLabel:"Timestamp (ms)",dateGmtLabel:"GMT",dateLocalLabel:"Local Timezone",dateRelativeLabel:"Relative Time",aboutTitle:"About Unix Timestamp",whatIsUnixTitle:"What is Unix Timestamp?",whatIsUnixDesc:"Unix timestamp is an integer representing the number of seconds elapsed since January 1, 1970 00:00:00 UTC (the Unix Epoch).",timeRangeTitle:"Time Range",timeRangeStart:"- Start time: January 1, 1970 00:00:00 UTC, timestamp: 0",timeRangeEnd:"- End time: January 19, 2038 03:14:07 UTC, timestamp: 2,147,483,647",timeRangeNote:"* Note: This limitation is based on 32-bit systems. 64-bit systems can represent ±292,277,026,596 years.",commonUnitsTitle:"Common Units",unitSeconds:"Seconds: Most commonly used, 10 digits",unitMilliseconds:"Milliseconds: 1/1000 of a second, 13 digits",unitMicroseconds:"Microseconds: 1/1,000,000 of a second, 16 digits",unitNanoseconds:"Nanoseconds: 1/1,000,000,000 of a second, 19 digits",whyUseTitle:"Why Use Timestamps?",whyUse1:"Unified standard: Not affected by time zones",whyUse2:"Easy calculation: Can be directly compared",whyUse3:"Storage efficient: Represents complete date and time with a single number",whyUse4:"Cross-platform: Supported by all mainstream programming languages",y2038Title:"Year 2038 Problem",y2038Desc:"On 32-bit systems, Unix timestamp will reach its maximum value of 2,147,483,647 on January 19, 2038 03:14:07 UTC, potentially causing overflow issues. Modern 64-bit systems are not affected by this limitation.",footerText:"© 2025 Unix Timestamp Converter - A useful tool for developers and system administrators.",formatSeconds:"Seconds (10 digits)",formatMilliseconds:"Milliseconds (13 digits)",formatMicroseconds:"Microseconds (16 digits)",formatNanoseconds:"Nanoseconds (19 digits)",formatError:"Invalid timestamp format",futurePrefix:"Future",pastPrefix:"Past",yearsUnit:"years",monthsUnit:"months",daysUnit:"days",hoursUnit:"hours",minutesUnit:"minutes",secondsUnit:"seconds",oneMinLater:"1 min later",threeMinLater:"3 min later",fiveMinLater:"5 min later",customMinLater:"min later",customCopyBtn:"Copy",customCopied:"Copied!",enterNumber:"Enter #",customPlaceholder:"Custom",pcHint:"💡 Tip: Double-click to copy 3min later timestamp",mobileHint:"💡 Tip: Double-click to copy 3min later timestamp",featuresBtn:"✨ Features",caseConverterBtn:"🔤 Case Converter",dateInputHint:"Select date and time"},"zh-CN":{seoTitle:"Unix 时间戳转换器 - 免费在线工具",seoDescription:"免费在线Unix时间戳转换工具，支持秒、毫秒、微秒和纳秒的转换，适合开发者和系统管理员。",seoKeywords:"Unix时间戳, 时间戳转换, epoch转换, 日期转换, 开发者工具",pageTitle:"Unix 时间戳转换器",currentSecondsLabel:"当前时间戳（秒）",currentMillisecondsLabel:"当前时间戳（毫秒）",currentTimeLabel:"当前时间",copyBtn:"复制",copied:"已复制",timestampConversionTitle:"时间戳转换",timestampConversionDesc:"支持秒、毫秒、微秒和纳秒的时间戳",timestampInputPlaceholder:"输入时间戳",formatLabel:"格式",gmtLabel:"格林威治标准时间",localTimezoneLabel:"本地时区",relativeTimeLabel:"相对当前时间",dateConversionTitle:"日期转换",dateInputPlaceholder:"YYYY-MM-DD HH:mm:ss / 年-月-日 时:分:秒",secondsLabel:"时间戳（秒）",millisecondsLabel:"时间戳（毫秒）",dateGmtLabel:"格林威治标准时间",dateLocalLabel:"本地时区",dateRelativeLabel:"相对当前时间",aboutTitle:"关于 Unix 时间戳",whatIsUnixTitle:"什么是 Unix 时间戳?",whatIsUnixDesc:"Unix 时间戳是一个整数，表示自 1970 年 1 月 1 日 00:00:00 UTC（协调世界时）以来经过的秒数，称为 Unix 纪元或 POSIX 时间。",timeRangeTitle:"时间范围",timeRangeStart:"- 开始时间: 1970年1月1日 00:00:00 UTC，时间戳: 0",timeRangeEnd:"- 结束时间: 2038年1月19日 03:14:07 UTC，时间戳: 2,147,483,647",timeRangeNote:"* 注意: 在基于32位系统的限制，64位系统可表示范围为±292,277,026,596年",commonUnitsTitle:"常用单位",unitSeconds:"秒: 最常用单位，10位数字",unitMilliseconds:"毫秒: 千分之一秒，13位数字",unitMicroseconds:"微秒: 百万分之一秒，16位数字",unitNanoseconds:"纳秒: 十亿分之一秒，19位数字",whyUseTitle:"为何使用时间戳",whyUse1:"统一标准: 不受时区影响",whyUse2:"易计算: 可直接比较大小",whyUse3:"存储高效: 用单个数字表示完整日期时间",whyUse4:"跨平台: 所有主流编程语言均均支持",y2038Title:"2038年问题",y2038Desc:"在32位系统上，Unix时间戳将在2038年1月19日03:14:07 UTC达到最大值2,147,483,647，可能导致溢出问题，现代64位系统不受此限制影响。",footerText:"© 2025 Unix 时间戳转换器 - 为开发者和系统管理员提供的实用工具。",formatSeconds:"秒（10位数字）",formatMilliseconds:"毫秒（13位数字）",formatMicroseconds:"微秒（16位数字）",formatNanoseconds:"纳秒（19位数字）",formatError:"无效的时间戳格式",futurePrefix:"未来",pastPrefix:"过去",yearsUnit:"年",monthsUnit:"个月",daysUnit:"天",hoursUnit:"小时",minutesUnit:"分钟",secondsUnit:"秒",oneMinLater:"1分钟后",threeMinLater:"3分钟后",fiveMinLater:"5分钟后",customMinLater:"分钟后",customCopyBtn:"复制",customCopied:"已复制！",enterNumber:"输入数字",customPlaceholder:"自定义",pcHint:"💡 提示：双击复制3分钟后时间戳",mobileHint:"💡 提示：双击复制3分钟后时间戳",featuresBtn:"✨ 功能特性",caseConverterBtn:"🔤 大小写转换",dateInputHint:"选择日期和时间"},"hi-IN":{seoTitle:"यूनिक्स टाइमस्टैम्प कनवर्टर - निशुल्क ऑनलाइन टूल",seoDescription:"नि:शुल्क ऑनलाइन यूनिक्स टाइमस्टैम्प कनवर्टर, सेकंड, मिलीसेकंड, माइक्रोसेकंड और नैनोसेकंड के लिए समर्थन।",seoKeywords:"Unix टाइमस्टैम्प, टाइमस्टैम्प कनवर्टर, epoch कनवर्टर, दिनांक कनवर्टर, डेवलपर टूल",pageTitle:"यूनिक्स टाइमस्टैम्प कनवर्टर",currentSecondsLabel:"वर्तमान टाइमस्टैम्प (सेकंड)",currentMillisecondsLabel:"वर्तमान टाइमस्टैम्प (मिलीसेकंड)",currentTimeLabel:"वर्तमान समय",copyBtn:"कॉपी करें",copied:"कॉपी किया गया",timestampConversionTitle:"टाइमस्टैम्प रूपांतरण",timestampConversionDesc:"सेकंड, मिलीसेकंड, माइक्रोसेकंड और नैनोसेकंड का समर्थन करता है",timestampInputPlaceholder:"टाइमस्टैम्प दर्ज करें",formatLabel:"प्रारूप",gmtLabel:"जीएमटी",localTimezoneLabel:"स्थानीय समय क्षेत्र",relativeTimeLabel:"सापेक्ष समय",dateConversionTitle:"दिनांक रूपांतरण",dateInputPlaceholder:"YYYY-MM-DD HH:mm:ss",secondsLabel:"टाइमस्टैम्प (सेकंड)",millisecondsLabel:"टाइमस्टैम्प (मिलीसेकंड)",dateGmtLabel:"जीएमटी",dateLocalLabel:"स्थानीय समय क्षेत्र",dateRelativeLabel:"सापेक्ष समय",aboutTitle:"यूनिक्स टाइमस्टैम्प के बारे में",whatIsUnixTitle:"यूनिक्स टाइमस्टैम्प क्या है?",whatIsUnixDesc:"यूनिक्स टाइमस्टैम्प एक पूर्णांक है जो 1 जनवरी, 1970 00:00:00 UTC (यूनिक्स एपोक) के बाद से बीते सेकंडों की संख्या का प्रतिनिधित्व करता है।",timeRangeTitle:"समय सीमा",timeRangeStart:"- प्रारंभ समय: 1 जनवरी, 1970 00:00:00 UTC, टाइमस्टैम्प: 0",timeRangeEnd:"- अंतिम समय: 19 जनवरी, 2038 03:14:07 UTC, टाइमस्टैम्प: 2,147,483,647",timeRangeNote:"* नोट: यह सीमा 32-बिट सिस्टम पर आधारित है। 64-बिट सिस्टम ±292,277,026,596 वर्षों का प्रतिनिधित्व कर सकते हैं।",commonUnitsTitle:"सामान्य इकाइयाँ",unitSeconds:"सेकंड: सबसे अधिक उपयोग किया जाता है, 10 अंक",unitMilliseconds:"मिलीसेकंड: 1/1000 सेकंड, 13 अंक",unitMicroseconds:"माइक्रोसेकंड: 1/1,000,000 सेकंड, 16 अंक",unitNanoseconds:"नैनोसेकंड: 1/1,000,000,000 सेकंड, 19 अंक",whyUseTitle:"टाइमस्टैम्प का उपयोग क्यों करें?",whyUse1:"एकीकृत मानक: समय क्षेत्रों से प्रभावित नहीं",whyUse2:"आसान गणना: सीधे तुलना की जा सकती है",whyUse3:"स्टोरेज कुशल: एक ही संख्या से पूरी तारीख और समय का प्रतिनिधित्व करता है",whyUse4:"क्रॉस-प्लेटफॉर्म: सभी मुख्यधारा की प्रोग्रामिंग भाषाओं द्वारा समर्थित",y2038Title:"वर्ष 2038 की समस्या",y2038Desc:"32-बिट सिस्टम पर, यूनिक्स टाइमस्टैम्प 19 जनवरी, 2038 03:14:07 UTC पर अपने अधिकतम मान 2,147,483,647 तक पहुंच जाएगा, जिससे ओवरफ्लो समस्याएं हो सकती हैं। आधुनिक 64-बिट सिस्टम इस सीमा से प्रभावित नहीं हैं।",footerText:"© 2025 यूनिक्स टाइमस्टैम्प कनवर्टर - डेवलपर्स और सिस्टम प्रशासकों के लिए एक उपयोगी उपकरण।",formatSeconds:"सेकंड (10 अंक)",formatMilliseconds:"मिलीसेकंड (13 अंक)",formatMicroseconds:"माइक्रोसेकंड (16 अंक)",formatNanoseconds:"नैनोसेकंड (19 अंक)",formatError:"अमान्य टाइमस्टैम्प प्रारूप",futurePrefix:"भविष्य",pastPrefix:"पिछला",yearsUnit:"साल",monthsUnit:"महीने",daysUnit:"दिन",hoursUnit:"घंटे",minutesUnit:"मिनट",secondsUnit:"सेकंड",oneMinLater:"1 मिनट बाद",threeMinLater:"3 मिनट बाद",fiveMinLater:"5 मिनट बाद",customMinLater:"मिनट बाद",customCopyBtn:"कॉपी करें",customCopied:"कॉपी हो गया!",enterNumber:"संख्या दर्ज करें",customPlaceholder:"कस्टम",pcHint:"💡 सुझाव: 3 मिनट बाद टाइमस्टैम्प कॉपी करने के लिए डबल-क्लिक करें",mobileHint:"💡 सुझाव: 3 मिनट बाद टाइमस्टैम्प कॉपी करने के लिए डबल-क्लिक करें",dateInputHint:"दिनांक और समय चुनें"},"ja-JP":{seoTitle:"Unixタイムスタンプコンバーター - 無料オンラインツール",seoDescription:"無料オンラインUnixタイムスタンプコンバーター。秒、ミリ秒、マイクロ秒、ナノ秒の変換をサポート。",seoKeywords:"Unixタイムスタンプ, タイムスタンプ変換, epoch変換, 日付変換, 開発者ツール",pageTitle:"Unixタイムスタンプコンバーター",currentSecondsLabel:"現在のタイムスタンプ（秒）",currentMillisecondsLabel:"現在のタイムスタンプ（ミリ秒）",currentTimeLabel:"現在時刻",copyBtn:"コピー",copied:"コピー済み",timestampConversionTitle:"タイムスタンプ変換",timestampConversionDesc:"秒、ミリ秒、マイクロ秒、ナノ秒をサポート",timestampInputPlaceholder:"タイムスタンプを入力",formatLabel:"フォーマット",gmtLabel:"GMT",localTimezoneLabel:"ローカルタイムゾーン",relativeTimeLabel:"相対時間",dateConversionTitle:"日付変換",dateInputPlaceholder:"YYYY-MM-DD HH:mm:ss",secondsLabel:"タイムスタンプ（秒）",millisecondsLabel:"タイムスタンプ（ミリ秒）",dateGmtLabel:"GMT",dateLocalLabel:"ローカルタイムゾーン",dateRelativeLabel:"相対時間",aboutTitle:"Unixタイムスタンプについて",whatIsUnixTitle:"Unixタイムスタンプとは？",whatIsUnixDesc:"Unixタイムスタンプは、1970年1月1日 00:00:00 UTC（Unix紀元）以降の経過秒数を表す整数です。",timeRangeTitle:"時間範囲",timeRangeStart:"- 開始時間: 1970年1月1日 00:00:00 UTC、タイムスタンプ: 0",timeRangeEnd:"- 終了時間: 2038年1月19日 03:14:07 UTC、タイムスタンプ: 2,147,483,647",timeRangeNote:"* 注意: この制限は32ビットシステムに基づいています。64ビットシステムでは±292,277,026,596年を表現できます。",commonUnitsTitle:"一般的な単位",unitSeconds:"秒: 最も一般的に使用される、10桁",unitMilliseconds:"ミリ秒: 1/1000秒、13桁",unitMicroseconds:"マイクロ秒: 1/1,000,000秒、16桁",unitNanoseconds:"ナノ秒: 1/1,000,000,000秒、19桁",whyUseTitle:"タイムスタンプを使用する理由",whyUse1:"統一規格: タイムゾーンの影響を受けない",whyUse2:"計算が容易: 直接比較可能",whyUse3:"ストレージ効率: 単一の数字で完全な日付と時刻を表現",whyUse4:"クロスプラットフォーム: すべての主要なプログラミング言語でサポート",y2038Title:"2038年問題",y2038Desc:"32ビットシステムでは、Unixタイムスタンプは2038年1月19日03:14:07 UTCに最大値2,147,483,647に達し、オーバーフロー問題を引き起こす可能性があります。現代の64ビットシステムはこの制限の影響を受けません。",footerText:"© 2025 Unixタイムスタンプコンバーター - 開発者とシステム管理者のための便利なツール。",formatSeconds:"秒（10桁）",formatMilliseconds:"ミリ秒（13桁）",formatMicroseconds:"マイクロ秒（16桁）",formatNanoseconds:"ナノ秒（19桁）",formatError:"無効なタイムスタンプ形式",futurePrefix:"未来",pastPrefix:"過去",yearsUnit:"年",monthsUnit:"ヶ月",daysUnit:"日",hoursUnit:"時間",minutesUnit:"分",secondsUnit:"秒",oneMinLater:"1分後",threeMinLater:"3分後",fiveMinLater:"5分後",customMinLater:"分後",customCopyBtn:"コピー",customCopied:"コピー済み！",enterNumber:"数字を入力",customPlaceholder:"カスタム",pcHint:"💡 ヒント：ダブルクリックで3分後のタイムスタンプをコピー",mobileHint:"💡 ヒント：ダブルクリックで3分後のタイムスタンプをコピー",dateInputHint:"日付と時刻を選択"},"de-DE":{seoTitle:"Unix-Zeitstempel-Konverter - Kostenloses Online-Tool",seoDescription:"Kostenloser Unix-Zeitstempel-Konverter online. Unterstützt Sekunden, Millisekunden, Mikrosekunden und Nanosekunden.",seoKeywords:"Unix Zeitstempel, Zeitstempel Konverter, Epoch Konverter, Datum Konverter, Entwickler Tools",pageTitle:"Unix-Zeitstempel-Konverter",currentSecondsLabel:"Aktueller Zeitstempel (s)",currentMillisecondsLabel:"Aktueller Zeitstempel (ms)",currentTimeLabel:"Aktuelle Zeit",copyBtn:"Kopieren",copied:"Kopiert",timestampConversionTitle:"Zeitstempel-Konvertierung",timestampConversionDesc:"Unterstützt Sekunden, Millisekunden, Mikrosekunden und Nanosekunden",timestampInputPlaceholder:"Zeitstempel eingeben",formatLabel:"Format",gmtLabel:"GMT",localTimezoneLabel:"Lokale Zeitzone",relativeTimeLabel:"Relative Zeit",dateConversionTitle:"Datumskonvertierung",dateInputPlaceholder:"JJJJ-MM-TT HH:mm:ss",secondsLabel:"Zeitstempel (s)",millisecondsLabel:"Zeitstempel (ms)",dateGmtLabel:"GMT",dateLocalLabel:"Lokale Zeitzone",dateRelativeLabel:"Relative Zeit",aboutTitle:"Über Unix-Zeitstempel",whatIsUnixTitle:"Was ist ein Unix-Zeitstempel?",whatIsUnixDesc:"Ein Unix-Zeitstempel ist eine Ganzzahl, die die Anzahl der Sekunden darstellt, die seit dem 1. Januar 1970 00:00:00 UTC (der Unix-Epoche) vergangen sind.",timeRangeTitle:"Zeitbereich",timeRangeStart:"- Startzeit: 1. Januar 1970 00:00:00 UTC, Zeitstempel: 0",timeRangeEnd:"- Endzeit: 19. Januar 2038 03:14:07 UTC, Zeitstempel: 2.147.483.647",timeRangeNote:"* Hinweis: Diese Einschränkung basiert auf 32-Bit-Systemen. 64-Bit-Systeme können ±292.277.026.596 Jahre darstellen.",commonUnitsTitle:"Häufig verwendete Einheiten",unitSeconds:"Sekunden: Am häufigsten verwendet, 10 Stellen",unitMilliseconds:"Millisekunden: 1/1000 einer Sekunde, 13 Stellen",unitMicroseconds:"Mikrosekunden: 1/1.000.000 einer Sekunde, 16 Stellen",unitNanoseconds:"Nanosekunden: 1/1.000.000.000 einer Sekunde, 19 Stellen",whyUseTitle:"Warum Zeitstempel verwenden?",whyUse1:"Einheitlicher Standard: Nicht von Zeitzonen beeinflusst",whyUse2:"Einfache Berechnung: Kann direkt verglichen werden",whyUse3:"Speichereffizient: Stellt vollständiges Datum und Uhrzeit mit einer einzigen Zahl dar",whyUse4:"Plattformübergreifend: Von allen gängigen Programmiersprachen unterstützt",y2038Title:"Jahr-2038-Problem",y2038Desc:"Auf 32-Bit-Systemen wird der Unix-Zeitstempel am 19. Januar 2038 03:14:07 UTC seinen Maximalwert von 2.147.483.647 erreichen, was zu Überlaufproblemen führen kann. Moderne 64-Bit-Systeme sind von dieser Einschränkung nicht betroffen.",footerText:"© 2025 Unix-Zeitstempel-Konverter - Ein nützliches Tool für Entwickler und Systemadministratoren.",formatSeconds:"Sekunden (10 Stellen)",formatMilliseconds:"Millisekunden (13 Stellen)",formatMicroseconds:"Mikrosekunden (16 Stellen)",formatNanoseconds:"Nanosekunden (19 Stellen)",formatError:"Ungültiges Zeitstempelformat",futurePrefix:"Zukunft",pastPrefix:"Vergangenheit",yearsUnit:"Jahre",monthsUnit:"Monate",daysUnit:"Tage",hoursUnit:"Stunden",minutesUnit:"Minuten",secondsUnit:"Sekunden",oneMinLater:"1 Min später",threeMinLater:"3 Min später",fiveMinLater:"5 Min später",customMinLater:"Min später",customCopyBtn:"Kopieren",customCopied:"Kopiert!",enterNumber:"Zahl eingeben",customPlaceholder:"Benutzerdefiniert",pcHint:"💡 Tipp: Doppelklick zum Kopieren des 3-Minuten-späteren Zeitstempels",mobileHint:"💡 Tipp: Doppelklick zum Kopieren des 3-Minuten-späteren Zeitstempels",dateInputHint:"Datum und Uhrzeit auswählen"},"en-GB":{seoTitle:"Unix Timestamp Converter - Free Online Tool",seoDescription:"Free online Unix timestamp converter. Convert between Unix timestamps and human-readable dates. Supports seconds, milliseconds, microseconds, and nanoseconds.",seoKeywords:"Unix timestamp, timestamp converter, epoch converter, date converter, developer tools",pageTitle:"Unix Timestamp Converter",currentSecondsLabel:"Current Timestamp (s)",currentMillisecondsLabel:"Current Timestamp (ms)",currentTimeLabel:"Current Time",copyBtn:"Copy",copied:"Copied",timestampConversionTitle:"Timestamp Conversion",timestampConversionDesc:"Supports seconds, milliseconds, microseconds and nanoseconds",timestampInputPlaceholder:"Enter timestamp",formatLabel:"Format",gmtLabel:"GMT",localTimezoneLabel:"Local Timezone",relativeTimeLabel:"Relative Time",dateConversionTitle:"Date Conversion",dateInputPlaceholder:"YYYY-MM-DD HH:mm:ss",secondsLabel:"Timestamp (s)",millisecondsLabel:"Timestamp (ms)",dateGmtLabel:"GMT",dateLocalLabel:"Local Timezone",dateRelativeLabel:"Relative Time",aboutTitle:"About Unix Timestamp",whatIsUnixTitle:"What is Unix Timestamp?",whatIsUnixDesc:"Unix timestamp is an integer representing the number of seconds elapsed since 1 January 1970 00:00:00 UTC (the Unix Epoch).",timeRangeTitle:"Time Range",timeRangeStart:"- Start time: 1 January 1970 00:00:00 UTC, timestamp: 0",timeRangeEnd:"- End time: 19 January 2038 03:14:07 UTC, timestamp: 2,147,483,647",timeRangeNote:"* Note: This limitation is based on 32-bit systems. 64-bit systems can represent ±292,277,026,596 years.",commonUnitsTitle:"Common Units",unitSeconds:"Seconds: Most commonly used, 10 digits",unitMilliseconds:"Milliseconds: 1/1000 of a second, 13 digits",unitMicroseconds:"Microseconds: 1/1,000,000 of a second, 16 digits",unitNanoseconds:"Nanoseconds: 1/1,000,000,000 of a second, 19 digits",whyUseTitle:"Why Use Timestamps?",whyUse1:"Unified standard: Not affected by time zones",whyUse2:"Easy calculation: Can be directly compared",whyUse3:"Storage efficient: Represents complete date and time with a single number",whyUse4:"Cross-platform: Supported by all mainstream programming languages",y2038Title:"Year 2038 Problem",y2038Desc:"On 32-bit systems, Unix timestamp will reach its maximum value of 2,147,483,647 on 19 January 2038 03:14:07 UTC, potentially causing overflow issues. Modern 64-bit systems are not affected by this limitation.",footerText:"© 2025 Unix Timestamp Converter - A useful tool for developers and system administrators.",formatSeconds:"Seconds (10 digits)",formatMilliseconds:"Milliseconds (13 digits)",formatMicroseconds:"Microseconds (16 digits)",formatNanoseconds:"Nanoseconds (19 digits)",formatError:"Invalid timestamp format",futurePrefix:"Future",pastPrefix:"Past",yearsUnit:"years",monthsUnit:"months",daysUnit:"days",hoursUnit:"hours",minutesUnit:"minutes",secondsUnit:"seconds",oneMinLater:"1 min later",threeMinLater:"3 min later",fiveMinLater:"5 min later",customMinLater:"min later",customCopyBtn:"Copy",customCopied:"Copied!",enterNumber:"Enter #",customPlaceholder:"Custom",pcHint:"💡 Tip: Double-click to copy 3min later timestamp",mobileHint:"💡 Tip: Double-click to copy 3min later timestamp",dateInputHint:"Select date and time"},"ru-RU":{seoTitle:"Конвертер Unix-времени - Бесплатный онлайн-инструмент",seoDescription:"Бесплатный онлайн-конвертер Unix-времени. Поддержка секунд, миллисекунд, микросекунд и наносекунд.",seoKeywords:"Unix время, конвертер времени, epoch конвертер, конвертер дат, инструменты разработчика",pageTitle:"Конвертер временных меток Unix",currentSecondsLabel:"Текущая метка времени (с)",currentMillisecondsLabel:"Текущая метка времени (мс)",currentTimeLabel:"Текущее время",copyBtn:"Копировать",copied:"Скопировано",timestampConversionTitle:"Преобразование временной метки",timestampConversionDesc:"Поддерживает секунды, миллисекунды, микросекунды и наносекунды",timestampInputPlaceholder:"Введите временную метку",formatLabel:"Формат",gmtLabel:"GMT",localTimezoneLabel:"Местный часовой пояс",relativeTimeLabel:"Относительное время",dateConversionTitle:"Преобразование даты",dateInputPlaceholder:"ГГГГ-ММ-ДД ЧЧ:мм:сс",secondsLabel:"Временная метка (с)",millisecondsLabel:"Временная метка (мс)",dateGmtLabel:"GMT",dateLocalLabel:"Местный часовой пояс",dateRelativeLabel:"Относительное время",aboutTitle:"О временных метках Unix",whatIsUnixTitle:"Что такое временная метка Unix?",whatIsUnixDesc:"Временная метка Unix - это целое число, представляющее количество секунд, прошедших с 1 января 1970 года 00:00:00 UTC (эпоха Unix).",timeRangeTitle:"Временной диапазон",timeRangeStart:"- Время начала: 1 января 1970 года 00:00:00 UTC, временная метка: 0",timeRangeEnd:"- Время окончания: 19 января 2038 года 03:14:07 UTC, временная метка: 2,147,483,647",timeRangeNote:"* Примечание: Это ограничение основано на 32-битных системах. 64-битные системы могут представлять ±292,277,026,596 лет.",commonUnitsTitle:"Общие единицы",unitSeconds:"Секунды: Наиболее часто используемые, 10 цифр",unitMilliseconds:"Миллисекунды: 1/1000 секунды, 13 цифр",unitMicroseconds:"Микросекунды: 1/1,000,000 секунды, 16 цифр",unitNanoseconds:"Наносекунды: 1/1,000,000,000 секунды, 19 цифр",whyUseTitle:"Почему используют временные метки?",whyUse1:"Единый стандарт: Не зависит от часовых поясов",whyUse2:"Простой расчет: Можно напрямую сравнивать",whyUse3:"Эффективное хранение: Представляет полную дату и время одним числом",whyUse4:"Кроссплатформенность: Поддерживается всеми основными языками программирования",y2038Title:"Проблема 2038 года",y2038Desc:"На 32-битных системах временная метка Unix достигнет своего максимального значения 2,147,483,647 19 января 2038 года 03:14:07 UTC, что потенциально может вызвать проблемы переполнения. Современные 64-битные системы не подвержены этому ограничению.",footerText:"© 2025 Конвертер временных меток Unix - Полезный инструмент для разработчиков и системных администраторов.",formatSeconds:"Секунды (10 цифр)",formatMilliseconds:"Миллисекунды (13 цифр)",formatMicroseconds:"Микросекунды (16 цифр)",formatNanoseconds:"Наносекунды (19 цифр)",formatError:"Недействительный формат временной метки",futurePrefix:"Будущее",pastPrefix:"Прошлое",yearsUnit:"лет",monthsUnit:"месяцев",daysUnit:"дней",hoursUnit:"часов",minutesUnit:"минут",secondsUnit:"секунд",oneMinLater:"1 мин позже",threeMinLater:"3 мин позже",fiveMinLater:"5 мин позже",customMinLater:"мин позже",customCopyBtn:"Копировать",customCopied:"Скопировано!",enterNumber:"Введите #",customPlaceholder:"Пользовательский",pcHint:"💡 Совет: Дважды щелкните для копирования временной метки через 3 минуты",mobileHint:"💡 Совет: Дважды щелкните для копирования временной метки через 3 минуты",dateInputHint:"Выберите дату и время"},"ko-KR":{seoTitle:"Unix 타임스탬프 변환기 - 무료 온라인 도구",seoDescription:"무료 온라인 Unix 타임스탬프 변환기. 초, 밀리초, 마이크로초, 나노초 지원.",seoKeywords:"Unix 타임스탬프, 타임스탬프 변환, epoch 변환, 날짜 변환, 개발자 도구",pageTitle:"Unix 타임스탬프 변환기",currentSecondsLabel:"현재 타임스탬프 (초)",currentMillisecondsLabel:"현재 타임스탬프 (밀리초)",currentTimeLabel:"현재 시간",copyBtn:"복사",copied:"복사됨",timestampConversionTitle:"타임스탬프 변환",timestampConversionDesc:"초, 밀리초, 마이크로초 및 나노초 지원",timestampInputPlaceholder:"타임스탬프 입력",formatLabel:"형식",gmtLabel:"GMT",localTimezoneLabel:"현지 시간대",relativeTimeLabel:"상대 시간",dateConversionTitle:"날짜 변환",dateInputPlaceholder:"YYYY-MM-DD HH:mm:ss",secondsLabel:"타임스탬프 (초)",millisecondsLabel:"타임스탬프 (밀리초)",dateGmtLabel:"GMT",dateLocalLabel:"현지 시간대",dateRelativeLabel:"상대 시간",aboutTitle:"Unix 타임스탬프 소개",whatIsUnixTitle:"Unix 타임스탬프란?",whatIsUnixDesc:"Unix 타임스탬프는 1970년 1월 1일 00:00:00 UTC(Unix 에포크) 이후 경과된 초 수를 나타내는 정수입니다.",timeRangeTitle:"시간 범위",timeRangeStart:"- 시작 시간: 1970년 1월 1일 00:00:00 UTC, 타임스탬프: 0",timeRangeEnd:"- 종료 시간: 2038년 1월 19일 03:14:07 UTC, 타임스탬프: 2,147,483,647",timeRangeNote:"* 참고: 이 제한은 32비트 시스템을 기반으로 합니다. 64비트 시스템은 ±292,277,026,596년을 표현할 수 있습니다.",commonUnitsTitle:"일반 단위",unitSeconds:"초: 가장 일반적으로 사용됨, 10자리",unitMilliseconds:"밀리초: 1/1000초, 13자리",unitMicroseconds:"마이크로초: 1/1,000,000초, 16자리",unitNanoseconds:"나노초: 1/1,000,000,000초, 19자리",whyUseTitle:"타임스탬프를 사용하는 이유?",whyUse1:"통일된 표준: 시간대의 영향을 받지 않음",whyUse2:"쉬운 계산: 직접 비교 가능",whyUse3:"저장 효율성: 단일 숫자로 전체 날짜와 시간 표현",whyUse4:"크로스 플랫폼: 모든 주요 프로그래밍 언어에서 지원",y2038Title:"2038년 문제",y2038Desc:"32비트 시스템에서 Unix 타임스탬프는 2038년 1월 19일 03:14:07 UTC에 최대값 2,147,483,647에 도달하여 오버플로우 문제를 일으킬 수 있습니다. 현대의 64비트 시스템은 이 제한의 영향을 받지 않습니다.",footerText:"© 2025 Unix 타임스탬프 변환기 - 개발자 및 시스템 관리자를 위한 유용한 도구.",formatSeconds:"초 (10자리)",formatMilliseconds:"밀리초 (13자리)",formatMicroseconds:"마이크로초 (16자리)",formatNanoseconds:"나노초 (19자리)",formatError:"잘못된 타임스탬프 형식",futurePrefix:"미래",pastPrefix:"과거",yearsUnit:"년",monthsUnit:"개월",daysUnit:"일",hoursUnit:"시간",minutesUnit:"분",secondsUnit:"초",oneMinLater:"1분 후",threeMinLater:"3분 후",fiveMinLater:"5분 후",customMinLater:"분 후",customCopyBtn:"복사",customCopied:"복사됨!",enterNumber:"숫자 입력",customPlaceholder:"사용자 정의",pcHint:"💡 팁: 더블클릭으로 3분 후 타임스탬프 복사",mobileHint:"💡 팁: 더블클릭으로 3분 후 타임스탬프 복사",dateInputHint:"날짜와 시간 선택"},"en-CA":{seoTitle:"Unix Timestamp Converter - Free Online Tool for Developers",seoDescription:"Free online Unix timestamp converter. Convert between Unix timestamps and human-readable dates. Supports seconds, milliseconds, microseconds, and nanoseconds.",seoKeywords:"Unix timestamp, timestamp converter, epoch converter, date converter, developer tools",pageTitle:"Unix Timestamp Converter",currentSecondsLabel:"Current Timestamp (s)",currentMillisecondsLabel:"Current Timestamp (ms)",currentTimeLabel:"Current Time",copyBtn:"Copy",copied:"Copied",timestampConversionTitle:"Timestamp Conversion",timestampConversionDesc:"Supports seconds, milliseconds, microseconds and nanoseconds",timestampInputPlaceholder:"Enter timestamp",formatLabel:"Format",gmtLabel:"GMT",localTimezoneLabel:"Local Timezone",relativeTimeLabel:"Relative Time",dateConversionTitle:"Date Conversion",dateInputPlaceholder:"YYYY-MM-DD HH:mm:ss",secondsLabel:"Timestamp (s)",millisecondsLabel:"Timestamp (ms)",dateGmtLabel:"GMT",dateLocalLabel:"Local Timezone",dateRelativeLabel:"Relative Time",aboutTitle:"About Unix Timestamp",whatIsUnixTitle:"What is Unix Timestamp?",whatIsUnixDesc:"Unix timestamp is an integer representing the number of seconds elapsed since January 1, 1970 00:00:00 UTC (the Unix Epoch).",timeRangeTitle:"Time Range",timeRangeStart:"- Start time: January 1, 1970 00:00:00 UTC, timestamp: 0",timeRangeEnd:"- End time: January 19, 2038 03:14:07 UTC, timestamp: 2,147,483,647",timeRangeNote:"* Note: This limitation is based on 32-bit systems. 64-bit systems can represent ±292,277,026,596 years.",commonUnitsTitle:"Common Units",unitSeconds:"Seconds: Most commonly used, 10 digits",unitMilliseconds:"Milliseconds: 1/1000 of a second, 13 digits",unitMicroseconds:"Microseconds: 1/1,000,000 of a second, 16 digits",unitNanoseconds:"Nanoseconds: 1/1,000,000,000 of a second, 19 digits",whyUseTitle:"Why Use Timestamps?",whyUse1:"Unified standard: Not affected by time zones",whyUse2:"Easy calculation: Can be directly compared",whyUse3:"Storage efficient: Represents complete date and time with a single number",whyUse4:"Cross-platform: Supported by all mainstream programming languages",y2038Title:"Year 2038 Problem",y2038Desc:"On 32-bit systems, Unix timestamp will reach its maximum value of 2,147,483,647 on January 19, 2038 03:14:07 UTC, potentially causing overflow issues. Modern 64-bit systems are not affected by this limitation.",footerText:"© 2025 Unix Timestamp Converter - A useful tool for developers and system administrators.",formatSeconds:"Seconds (10 digits)",formatMilliseconds:"Milliseconds (13 digits)",formatMicroseconds:"Microseconds (16 digits)",formatNanoseconds:"Nanoseconds (19 digits)",formatError:"Invalid timestamp format",futurePrefix:"Future",pastPrefix:"Past",yearsUnit:"years",monthsUnit:"months",daysUnit:"days",hoursUnit:"hours",minutesUnit:"minutes",secondsUnit:"seconds",oneMinLater:"1 min later",threeMinLater:"3 min later",fiveMinLater:"5 min later",customMinLater:"min later",customCopyBtn:"Copy",customCopied:"Copied!",enterNumber:"Enter #",customPlaceholder:"Custom",pcHint:"💡 Tip: Double-click to copy 3min later timestamp",mobileHint:"💡 Tip: Double-click to copy 3min later timestamp",dateInputHint:"Select date and time"},"fr-FR":{seoTitle:"Convertisseur de Timestamp Unix - Outil en ligne gratuit",seoDescription:"Outil gratuit de conversion de timestamp Unix en ligne. Prend en charge les secondes, millisecondes, microsecondes et nanosecondes.",seoKeywords:"Unix timestamp, convertisseur timestamp, convertisseur epoch, convertisseur date, outil développeur",pageTitle:"Convertisseur de Timestamp Unix",currentSecondsLabel:"Timestamp actuel (s)",currentMillisecondsLabel:"Timestamp actuel (ms)",currentTimeLabel:"Heure actuelle",copyBtn:"Copier",copied:"Copié",timestampConversionTitle:"Conversion de timestamp",timestampConversionDesc:"Prend en charge les secondes, millisecondes, microsecondes et nanosecondes",timestampInputPlaceholder:"Entrez un timestamp",formatLabel:"Format",gmtLabel:"GMT",localTimezoneLabel:"Fuseau horaire local",relativeTimeLabel:"Temps relatif",dateConversionTitle:"Conversion de date",dateInputPlaceholder:"AAAA-MM-JJ HH:mm:ss",secondsLabel:"Timestamp (s)",millisecondsLabel:"Timestamp (ms)",dateGmtLabel:"GMT",dateLocalLabel:"Fuseau horaire local",dateRelativeLabel:"Temps relatif",aboutTitle:"À propos du timestamp Unix",whatIsUnixTitle:"Qu'est-ce qu'un timestamp Unix ?",whatIsUnixDesc:"Le timestamp Unix est un entier représentant le nombre de secondes écoulées depuis le 1er janvier 1970 00:00:00 UTC (l'époque Unix).",timeRangeTitle:"Plage de temps",timeRangeStart:"- Heure de début: 1er janvier 1970 00:00:00 UTC, timestamp: 0",timeRangeEnd:"- Heure de fin: 19 janvier 2038 03:14:07 UTC, timestamp: 2 147 483 647",timeRangeNote:"* Remarque: Cette limitation est basée sur les systèmes 32 bits. Les systèmes 64 bits peuvent représenter ±292 277 026 596 ans.",commonUnitsTitle:"Unités courantes",unitSeconds:"Secondes: Le plus couramment utilisé, 10 chiffres",unitMilliseconds:"Millisecondes: 1/1000 d'une seconde, 13 chiffres",unitMicroseconds:"Microsecondes: 1/1 000 000 d'une seconde, 16 chiffres",unitNanoseconds:"Nanosecondes: 1/1 000 000 000 d'une seconde, 19 chiffres",whyUseTitle:"Pourquoi utiliser des timestamps ?",whyUse1:"Standard unifié: Non affecté par les fuseaux horaires",whyUse2:"Calcul facile: Peut être directement comparé",whyUse3:"Stockage efficace: Représente une date et une heure complètes avec un seul nombre",whyUse4:"Multi-plateforme: Pris en charge par tous les langages de programmation courants",y2038Title:"Problème de l'an 2038",y2038Desc:"Sur les systèmes 32 bits, le timestamp Unix atteindra sa valeur maximale de 2 147 483 647 le 19 janvier 2038 03:14:07 UTC, ce qui pourrait causer des problèmes de dépassement. Les systèmes 64 bits modernes ne sont pas affectés par cette limitation.",footerText:"© 2025 Convertisseur de Timestamp Unix - Un outil utile pour les développeurs et les administrateurs système.",formatSeconds:"Secondes (10 chiffres)",formatMilliseconds:"Millisecondes (13 chiffres)",formatMicroseconds:"Microsecondes (16 chiffres)",formatNanoseconds:"Nanosecondes (19 chiffres)",formatError:"Format de timestamp invalide",futurePrefix:"Futur",pastPrefix:"Passé",yearsUnit:"ans",monthsUnit:"mois",daysUnit:"jours",hoursUnit:"heures",minutesUnit:"minutes",secondsUnit:"secondes",oneMinLater:"1 min plus tard",threeMinLater:"3 min plus tard",fiveMinLater:"5 min plus tard",customMinLater:"min plus tard",customCopyBtn:"Copier",customCopied:"Copié!",enterNumber:"Entrez #",customPlaceholder:"Personnalisé",pcHint:"💡 Astuce: Double-cliquez pour copier le timestamp 3min plus tard",mobileHint:"💡 Astuce: Double-cliquez pour copier le timestamp 3min plus tard",dateInputHint:"Sélectionner la date et l'heure"}};let m="en-US";async function H(){try{const i=(await(await fetch("https://ipapi.co/json/")).json()).country_code;let o={CN:"zh-CN",JP:"ja-JP",IN:"hi-IN",DE:"de-DE",GB:"en-GB",RU:"ru-RU",KR:"ko-KR",CA:"en-CA",FR:"fr-FR",US:"en-US"}[i];(!o||!(o in g))&&(o="en-US"),m=o,f()}catch{m="en-US",f()}}localStorage.getItem("lang")?(m=localStorage.getItem("lang"),f()):H();(function(){if(B())return;const t=window.location.pathname.replace(/^\//,"").replace(/\/$/,"");if(["en-GB","zh-CN","hi-IN","ja-JP","de-DE","ru-RU","ko-KR","en-CA","fr-FR"].includes(t))localStorage.setItem("lang",t),m=t,f();else if(window.location.pathname==="/"||window.location.pathname===""){const n=localStorage.getItem("lang");n&&(m=n,f())}})();function z(){const e=g[m];if(e&&e.seoTitle&&(document.title=e.seoTitle),e&&e.seoDescription){let t=document.querySelector('meta[name="description"]');t&&t.setAttribute("content",e.seoDescription)}if(e&&e.seoKeywords){let t=document.querySelector('meta[name="keywords"]');t&&t.setAttribute("content",e.seoKeywords)}}function f(){z();const e=g[m];document.getElementById("page-title").textContent=e.pageTitle,document.title=e.pageTitle,document.getElementById("language-btn").textContent=document.querySelector(`.language-option[data-lang="${m}"]`).textContent,document.getElementById("current-seconds-label").textContent=e.currentSecondsLabel,document.getElementById("current-milliseconds-label").textContent=e.currentMillisecondsLabel,document.getElementById("current-time-label").textContent=e.currentTimeLabel,document.querySelectorAll(".copy-btn").forEach(i=>{i.textContent=e.copyBtn}),document.getElementById("timestamp-conversion-title").textContent=e.timestampConversionTitle,document.getElementById("timestamp-conversion-desc").textContent=e.timestampConversionDesc,document.getElementById("timestamp-input").placeholder=e.timestampInputPlaceholder,document.getElementById("format-label").textContent=e.formatLabel,document.getElementById("gmt-label").textContent=e.gmtLabel,document.getElementById("local-timezone-label").textContent=e.localTimezoneLabel,document.getElementById("relative-time-label").textContent=e.relativeTimeLabel,document.getElementById("date-conversion-title").textContent=e.dateConversionTitle,document.getElementById("date-input").placeholder=e.dateInputPlaceholder,document.getElementById("seconds-label").textContent=e.secondsLabel,document.getElementById("milliseconds-label").textContent=e.millisecondsLabel,document.getElementById("date-gmt-label").textContent=e.gmtLabel,document.getElementById("date-local-label").textContent=e.localTimezoneLabel,document.getElementById("date-relative-label").textContent=e.relativeTimeLabel,document.getElementById("about-title").textContent=e.aboutTitle,document.getElementById("what-is-unix-title").textContent=e.whatIsUnixTitle,document.getElementById("what-is-unix-desc").textContent=e.whatIsUnixDesc,document.getElementById("time-range-title").textContent=e.timeRangeTitle,document.getElementById("time-range-start").textContent=e.timeRangeStart,document.getElementById("time-range-end").textContent=e.timeRangeEnd,document.getElementById("time-range-note").textContent=e.timeRangeNote,document.getElementById("common-units-title").textContent=e.commonUnitsTitle,document.getElementById("unit-seconds").textContent=e.unitSeconds,document.getElementById("unit-milliseconds").textContent=e.unitMilliseconds,document.getElementById("unit-microseconds").textContent=e.unitMicroseconds,document.getElementById("unit-nanoseconds").textContent=e.unitNanoseconds,document.getElementById("why-use-title").textContent=e.whyUseTitle,document.getElementById("why-use-1").textContent=e.whyUse1,document.getElementById("why-use-2").textContent=e.whyUse2,document.getElementById("why-use-3").textContent=e.whyUse3,document.getElementById("why-use-4").textContent=e.whyUse4,document.getElementById("y2038-title").textContent=e.y2038Title,document.getElementById("y2038-desc").textContent=e.y2038Desc,document.getElementById("footer-text").textContent=e.footerText,A(e);const t=document.getElementById("timestamp-input");t.value.trim()&&k(t.value.trim())}function A(e){["seconds","milliseconds","datetime"].forEach(s=>{document.getElementById(`one-min-later-${s}`).textContent=e.oneMinLater,document.getElementById(`three-min-later-${s}`).textContent=e.threeMinLater,document.getElementById(`five-min-later-${s}`).textContent=e.fiveMinLater,document.getElementById(`custom-min-later-${s}`).textContent=e.customMinLater,document.getElementById(`custom-copy-btn-${s}`).textContent=e.customCopyBtn;const l=document.getElementById(`custom-${s}-input`);l&&(l.placeholder=e.customPlaceholder)});const i=document.getElementById("pc-hint-text");i&&e.pcHint&&(i.textContent=e.pcHint);const n=document.getElementById("mobile-hint-text");n&&e.mobileHint&&(n.textContent=e.mobileHint);const o=document.getElementById("features-btn");o&&e.featuresBtn&&(o.textContent=e.featuresBtn);const a=document.getElementById("case-converter-btn");a&&e.caseConverterBtn&&(a.textContent=e.caseConverterBtn);const r=document.getElementById("date-input-hint");r&&e.dateInputHint&&(r.textContent=e.dateInputHint)}document.getElementById("language-btn").addEventListener("click",function(){document.getElementById("language-dropdown").classList.toggle("show")});window.addEventListener("click",function(e){if(!e.target.matches(".language-btn")){const t=document.getElementById("language-dropdown");t.classList.contains("show")&&t.classList.remove("show")}});function B(){return window.location.hostname==="localhost"||window.location.hostname==="127.0.0.1"||window.location.hostname.includes("localhost")}document.querySelectorAll(".language-option").forEach(e=>{e.addEventListener("click",function(){const t=this.getAttribute("data-lang");localStorage.setItem("lang",t),B(),m=t,f(),document.getElementById("language-dropdown").classList.remove("show")})});function D(){const e=new Date,t=Math.floor(e.getTime()/1e3),i=e.getTime();document.getElementById("current-timestamp-seconds").textContent=t,document.getElementById("current-timestamp-milliseconds").textContent=i;const n=e.getFullYear(),o=String(e.getMonth()+1).padStart(2,"0"),a=String(e.getDate()).padStart(2,"0"),r=String(e.getHours()).padStart(2,"0"),s=String(e.getMinutes()).padStart(2,"0"),l=String(e.getSeconds()).padStart(2,"0");document.getElementById("current-datetime").textContent=`${n}-${o}-${a} ${r}:${s}:${l}`}D();setInterval(D,1e3);let y={seconds:10,milliseconds:10,datetime:10};function $(){const e=localStorage.getItem("customMinutesCache");if(e)try{y={...y,...JSON.parse(e)}}catch(t){console.warn("Failed to load custom minutes cache:",t)}document.getElementById("custom-seconds-input").value=y.seconds,document.getElementById("custom-milliseconds-input").value=y.milliseconds,document.getElementById("custom-datetime-input").value=y.datetime}function C(){localStorage.setItem("customMinutesCache",JSON.stringify(y))}function v(e,t){const i=new Date,n=new Date(i.getTime()+t*60*1e3);switch(e){case"seconds":return Math.floor(n.getTime()/1e3).toString();case"milliseconds":return n.getTime().toString();case"datetime":const o=n.getFullYear(),a=String(n.getMonth()+1).padStart(2,"0"),r=String(n.getDate()).padStart(2,"0"),s=String(n.getHours()).padStart(2,"0"),l=String(n.getMinutes()).padStart(2,"0"),c=String(n.getSeconds()).padStart(2,"0");return`${o}-${a}-${r} ${s}:${l}:${c}`;default:return""}}function h(e,t){function i(){t.feedbackTimeout||(t.originalState||(t.originalState={content:t.innerHTML,backgroundColor:t.style.backgroundColor||"",border:t.style.border||"",color:t.style.color||"",transform:t.style.transform||"",transition:t.style.transition||"",boxShadow:t.style.boxShadow||""}),t.innerHTML="✓",t.style.backgroundColor="#28a745",t.style.border="1px solid #28a745",t.style.color="white",t.style.transform="scale(1.1)",t.style.transition="all 0.2s ease",t.style.boxShadow="0 2px 8px rgba(40, 167, 69, 0.3)",t.feedbackTimeout=setTimeout(()=>{t.innerHTML=t.originalState.content,t.style.backgroundColor=t.originalState.backgroundColor,t.style.border=t.originalState.border,t.style.color=t.originalState.color,t.style.transform=t.originalState.transform,t.style.transition=t.originalState.transition,t.style.boxShadow=t.originalState.boxShadow,t.feedbackTimeout=null},1500))}navigator.clipboard&&navigator.clipboard.writeText?navigator.clipboard.writeText(e).then(()=>{i()}).catch(n=>{console.warn("Clipboard API failed, trying fallback:",n),w(e,i)}):w(e,i)}function w(e,t,i){const n=document.createElement("textarea");n.value=e,n.style.position="fixed",n.style.top="0",n.style.left="0",n.style.width="2em",n.style.height="2em",n.style.padding="0",n.style.border="none",n.style.outline="none",n.style.boxShadow="none",n.style.background="transparent",n.style.fontSize="16px",n.style.opacity="0",n.style.pointerEvents="none",document.body.appendChild(n);try{n.focus(),n.select(),n.setSelectionRange(0,99999),document.execCommand("copy")?t():S(e,t)}catch(o){console.error("Fallback copy failed:",o),S(e,t)}finally{document.body.removeChild(n)}}function S(e,t){const i=document.createElement("div");i.style.cssText=`
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10000;
        font-family: Arial, sans-serif;
    `;const n=document.createElement("div");n.style.cssText=`
        background: white;
        padding: 20px;
        border-radius: 8px;
        max-width: 90%;
        max-height: 80%;
        overflow: auto;
        box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    `;const o=document.createElement("h3");o.textContent="请手动复制以下内容：",o.style.marginTop="0";const a=document.createElement("textarea");a.value=e,a.style.cssText=`
        width: 100%;
        height: 100px;
        margin: 10px 0;
        padding: 8px;
        border: 1px solid #ccc;
        border-radius: 4px;
        font-family: monospace;
        font-size: 14px;
        resize: vertical;
    `,a.readOnly=!0;const r=document.createElement("div");r.style.textAlign="center";const s=document.createElement("button");s.textContent="关闭",s.style.cssText=`
        padding: 8px 16px;
        margin: 0 5px;
        border: 1px solid #ccc;
        border-radius: 4px;
        background: #f5f5f5;
        cursor: pointer;
    `;const l=document.createElement("button");l.textContent="全选",l.style.cssText=`
        padding: 8px 16px;
        margin: 0 5px;
        border: 1px solid #007bff;
        border-radius: 4px;
        background: #007bff;
        color: white;
        cursor: pointer;
    `,s.onclick=()=>{document.body.removeChild(i),t()},l.onclick=()=>{a.select(),a.setSelectionRange(0,99999)},i.onclick=c=>{c.target===i&&(document.body.removeChild(i),t())},r.appendChild(l),r.appendChild(s),n.appendChild(o),n.appendChild(a),n.appendChild(r),i.appendChild(n),document.body.appendChild(i),setTimeout(()=>{a.select(),a.setSelectionRange(0,99999)},100)}function x(){document.querySelectorAll(".copy-dropdown").forEach(e=>{e.classList.remove("show")})}document.querySelectorAll(".copy-btn").forEach(e=>{let t,i,n=!1;const o="ontouchstart"in window||navigator.maxTouchPoints>0;if(e.addEventListener("click",function(r){if(r.stopPropagation(),o){r.preventDefault();return}if(t){clearTimeout(t),t=null;return}t=setTimeout(()=>{const s=this.getAttribute("data-copy"),l=document.getElementById(s).textContent;h(l,this),t=null},300)}),e.addEventListener("dblclick",function(r){r.stopPropagation(),t&&(clearTimeout(t),t=null),a(this)}),o){let r=0,s=null;e.addEventListener("touchstart",function(l){i=Date.now(),n=!1}),e.addEventListener("touchmove",function(l){n=!0}),e.addEventListener("touchend",function(l){const c=Date.now()-i,p=Date.now();c<300&&!n&&(p-r<300?(l.preventDefault(),s&&(clearTimeout(s),s=null),a(this),navigator.vibrate&&navigator.vibrate(50),r=0):(r=p,s&&clearTimeout(s),s=setTimeout(()=>{const T=this.getAttribute("data-copy"),M=document.getElementById(T).textContent;h(M,this),s=null},300)))})}function a(r){const s=r.getAttribute("data-type"),l=document.getElementById(`copy-dropdown-${s}`);x(),l.classList.toggle("show")}});document.querySelectorAll(".copy-option:not(.custom-option)").forEach(e=>{e.addEventListener("click",function(t){t.stopPropagation();const i=parseInt(this.getAttribute("data-minutes")),n=this.getAttribute("data-type"),o=v(n,i),a=document.getElementById(`copy-${n}-btn`);h(o,a),x()})});document.querySelectorAll(".custom-minutes-input").forEach(e=>{function t(){const i=parseInt(e.value);if(isNaN(i)||i<1)return;const n=e.id.replace("custom-","").replace("-input","");y[n]=i,C();const o=v(n,i),a=document.getElementById(`copy-${n}-btn`);h(o,a),x()}e.addEventListener("keypress",function(i){i.key==="Enter"&&(i.preventDefault(),t())}),e.addEventListener("blur",function(){const i=parseInt(this.value);if(!isNaN(i)&&i>=1){const n=this.id.replace("custom-","").replace("-input","");y[n]=i,C()}}),e.addEventListener("input",function(){const i=parseInt(this.value);if(!isNaN(i)&&i>=1){const n=this.id.replace("custom-","").replace("-input","");y[n]=i,C();const o=v(n,i),a=document.getElementById(`copy-${n}-btn`);h(o,a)}}),e.addEventListener("click",function(i){i.stopPropagation()})});document.querySelectorAll(".custom-copy-btn").forEach(e=>{e.addEventListener("click",function(t){t.stopPropagation();const i=this.getAttribute("data-type"),n=document.getElementById(`custom-${i}-input`),o=parseInt(n.value);if(isNaN(o)||o<1){const l=this.textContent;this.textContent=g[m].enterNumber,setTimeout(()=>{this.textContent=l},1e3);return}y[i]=o,C();const a=v(i,o),r=document.getElementById(`copy-${i}-btn`);h(a,r);const s=this.textContent;this.textContent=g[m].customCopied,setTimeout(()=>{this.textContent=s},1500),setTimeout(()=>{x()},1e3)}),e.addEventListener("click",function(t){t.stopPropagation()})});document.addEventListener("click",function(e){e.target.closest(".copy-btn-container")||x()});document.querySelectorAll(".copy-dropdown").forEach(e=>{e.addEventListener("click",function(t){t.stopPropagation()})});function F(e){const t=parseInt(e,10);if(isNaN(t))return null;const i=e.length,n=new Date(t*1e3),o=n.getTime()>0&&n.getFullYear()>=1970&&n.getFullYear()<=2100,a=new Date(t),r=a.getTime()>0&&a.getFullYear()>=1970&&a.getFullYear()<=2100;return i<=10&&o?{type:"seconds",value:t*1e3}:i<=13&&r?{type:"milliseconds",value:t}:i<=16?{type:"microseconds",value:Math.floor(t/1e3)}:i<=19?{type:"nanoseconds",value:Math.floor(t/1e6)}:null}function k(e){const t=document.getElementById("timestamp-format"),i=F(e);if(!i)return t.textContent=g[m].formatError,t.className="format-error",null;let n="";switch(i.type){case"seconds":n=g[m].formatSeconds;break;case"milliseconds":n=g[m].formatMilliseconds;break;case"microseconds":n=g[m].formatMicroseconds;break;case"nanoseconds":n=g[m].formatNanoseconds;break}return t.textContent=n,t.className="format-detected",i}function L(e,t){const i=e-t,n=Math.abs(i),o=Math.floor(n/1e3),a=Math.floor(o/60),r=Math.floor(a/60),s=Math.floor(r/24),l=g[m],c=i>0?l.futurePrefix:l.pastPrefix;if(s>365){const p=Math.floor(s/365);return`${c} ${p} ${l.yearsUnit}`}else if(s>30){const p=Math.floor(s/30);return`${c} ${p} ${l.monthsUnit}`}else{if(s>0)return`${c} ${s} ${l.daysUnit}`;{const p=r%24,T=a%60,M=o%60,R=String(p).padStart(2,"0"),N=String(T).padStart(2,"0"),P=String(M).padStart(2,"0");return`${c} ${R}:${N}:${P}`}}}let d=null,u=null,U=null,b=null;function Y(){if(!U)return;const t=L(U,new Date().getTime());document.getElementById("timestamp-relative").textContent=t}function K(){if(!b)return;const t=L(b,new Date().getTime());document.getElementById("date-relative").textContent=t}document.getElementById("timestamp-input").addEventListener("input",function(){const e=this.value.trim();if(!e){E(),document.getElementById("timestamp-format").textContent="",document.getElementById("timestamp-format").className="",d&&(clearInterval(d),d=null),U=null;return}const t=k(e);if(!t){E(),d&&(clearInterval(d),d=null),U=null;return}const{value:i}=t,n=new Date(i),o=new Date;U=i;const a=n.toUTCString();document.getElementById("timestamp-gmt").textContent=a;const r=n.toLocaleString();document.getElementById("timestamp-local").textContent=r;const s=L(n.getTime(),o.getTime());document.getElementById("timestamp-relative").textContent=s,d&&clearInterval(d),d=setInterval(Y,1e3)});function E(){document.getElementById("timestamp-gmt").textContent="",document.getElementById("timestamp-local").textContent="",document.getElementById("timestamp-relative").textContent=""}document.getElementById("date-input").addEventListener("input",function(){const e=this.value.trim();if(!e){I(),u&&(clearInterval(u),u=null),b=null;return}const t=new Date(e);if(isNaN(t.getTime())){I(),u&&(clearInterval(u),u=null),b=null;return}const i=new Date;b=t.getTime();const n=Math.floor(t.getTime()/1e3);document.getElementById("date-timestamp-seconds").textContent=n;const o=t.getTime();document.getElementById("date-timestamp-milliseconds").textContent=o;const a=t.toUTCString();document.getElementById("date-gmt").textContent=a;const r=t.toLocaleString();document.getElementById("date-local").textContent=r;const s=L(t.getTime(),i.getTime());document.getElementById("date-relative").textContent=s,u&&clearInterval(u),u=setInterval(K,1e3)});function I(){document.getElementById("date-timestamp-seconds").textContent="",document.getElementById("date-timestamp-milliseconds").textContent="",document.getElementById("date-gmt").textContent="",document.getElementById("date-local").textContent="",document.getElementById("date-relative").textContent=""}f();document.addEventListener("DOMContentLoaded",function(){$();const e=new Date,t=Math.floor(e.getTime()/1e3),i=document.getElementById("timestamp-input");i.value=t;const n=new Event("input",{bubbles:!0});i.dispatchEvent(n);const o=e.getFullYear(),a=String(e.getMonth()+1).padStart(2,"0"),r=String(e.getDate()).padStart(2,"0"),s=String(e.getHours()).padStart(2,"0"),l=String(e.getMinutes()).padStart(2,"0"),c=`${o}-${a}-${r}T${s}:${l}`,p=document.getElementById("date-input");p.value=c;const T=new Event("input",{bubbles:!0});p.dispatchEvent(T)});window.addEventListener("beforeunload",function(){d&&(clearInterval(d),d=null),u&&(clearInterval(u),u=null)});
