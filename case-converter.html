<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="Free online tool to convert between snake_case and PascalCase text formats">
  <title>下划线转驼峰工具 - Unix Timestamp Converter</title>
  
  <!-- Language and localization -->
  <link rel="alternate" hreflang="en" href="https://unixtime.help/case-converter.html" />
  <link rel="alternate" hreflang="zh-CN" href="https://unixtime.help/zh-CN/case-converter.html" />
  <link rel="alternate" hreflang="x-default" href="https://unixtime.help/case-converter.html" />
  
  <link rel="icon" href="/icon.png" type="image/png">
  <link rel="stylesheet" href="/style.css">
  <style>
    /* Language Switcher Styles */
    .language-switcher {
      position: relative;
      display: inline-block;
      margin-left: 15px;
    }
    
    .language-btn {
      background: none;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 5px 10px;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 5px;
      font-size: 0.9em;
      background-color: #f8f9fa;
    }
    
    .language-btn:hover {
      background-color: #e9ecef;
    }
    
    .language-dropdown {
      display: none;
      position: absolute;
      background-color: white;
      min-width: 120px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      border-radius: 4px;
      z-index: 1000;
      right: 0;
      margin-top: 5px;
    }
    
    .language-switcher:hover .language-dropdown {
      display: block;
    }
    
    .language-option {
      display: block;
      padding: 8px 12px;
      text-decoration: none;
      color: #333;
    }
    
    .language-option:hover, .language-option.active {
      background-color: #f1f3f5;
    }
    
    /* Footer Styles */
    .footer {
      margin-top: 40px;
      padding: 20px 0;
      border-top: 1px solid #e9ecef;
      text-align: center;
      font-size: 0.9em;
      color: #6c757d;
    }
    
    .footer-links {
      margin: 10px 0;
    }
    
    .footer-link {
      color: #6c757d;
      text-decoration: none;
      margin: 0 5px;
    }
    
    .footer-link:hover {
      text-decoration: underline;
    }
    
    .divider {
      margin: 0 5px;
      color: #dee2e6;
    }
    
    .language-credit {
      margin-top: 10px;
      font-size: 0.9em;
    }
    
    .language-credit a {
      color: #007bff;
      text-decoration: none;
    }
    
    .language-credit a:hover {
      text-decoration: underline;
    }
    
    .converter-container {
      margin-top: 20px;
    }
    .converter-section {
      margin-bottom: 30px;
      padding: 20px;
      background-color: #f8f9fa;
      border-radius: 5px;
    }
    .converter-title {
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 15px;
      color: #333;
    }
    .input-group {
      margin-bottom: 15px;
    }
    .input-group label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
      color: #555;
    }
    .input-group textarea {
      width: 100%;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
      min-height: 80px;
      font-family: monospace;
    }
    .convert-btn {
      background-color: #007bff;
      color: white;
      border: none;
      border-radius: 4px;
      padding: 8px 15px;
      font-size: 14px;
      cursor: pointer;
      margin-right: 10px;
    }
    .convert-btn:hover {
      background-color: #0069d9;
    }
    .clear-btn {
      background-color: #6c757d;
      color: white;
      border: none;
      border-radius: 4px;
      padding: 8px 15px;
      font-size: 14px;
      cursor: pointer;
    }
    .clear-btn:hover {
      background-color: #5a6268;
    }
    .result-container {
      margin-top: 15px;
      padding: 10px;
      background-color: #fff;
      border: 1px solid #ddd;
      border-radius: 4px;
      min-height: 80px;
      font-family: monospace;
      white-space: pre-wrap; /* 确保换行符在CSS中也被正确显示 */
      word-wrap: break-word; /* 确保长文本会自动换行 */
    }
    .back-link {
      display: inline-block;
      margin-top: 20px;
      color: #007bff;
      text-decoration: none;
    }
    .back-link:hover {
      text-decoration: underline;
    }
  </style>
</head>
<body>
<div class="container">
  <div class="header">
    <div class="header-left">
      <h1 id="page-title"><a href="/" style="text-decoration: none; color: inherit;">Unix Timestamp Converter</a></h1>
    </div>

    <div class="header-right">
      <!-- Tools Selector -->
      <div class="tools-selector">
        <button class="tools-btn" id="tools-btn">🔧 Other Tools</button>
        <div class="tools-dropdown" id="tools-dropdown">
          <a href="/features.html" class="tools-option" id="tools-features">✨ Features</a>
          <a href="/case-converter.html" class="tools-option" id="tools-case-converter">🔤 Case Converter</a>
        </div>
      </div>

      <!-- Donation button -->
      <a href="/donate.html" class="header-btn donate-btn" id="donate-btn">❤️ Donate</a>

      <!-- Language Selector -->
      <div class="language-selector">
        <button class="language-btn" id="language-btn">English (US)</button>
        <div class="language-dropdown" id="language-dropdown">
          <div class="language-option" data-lang="en-US">English (US)</div>
          <div class="language-option" data-lang="zh-CN">中文 (中国)</div>
          <div class="language-option" data-lang="hi-IN">हिन्दी (भारत)</div>
          <div class="language-option" data-lang="ja-JP">日本語 (日本)</div>
          <div class="language-option" data-lang="de-DE">Deutsch (Deutschland)</div>
          <div class="language-option" data-lang="en-GB">English (UK)</div>
          <div class="language-option" data-lang="ru-RU">Русский (Россия)</div>
          <div class="language-option" data-lang="ko-KR">한국어 (대한민국)</div>
          <div class="language-option" data-lang="en-CA">English (Canada)</div>
          <div class="language-option" data-lang="fr-FR">Français (France)</div>
        </div>
      </div>
    </div>
  </div>

  <div class="converter-container">
    <div class="converter-section">
      <h2 class="converter-title" id="snake-to-camel-title">下划线转大驼峰</h2>
      <div class="input-group">
        <label for="snake-input" id="snake-to-camel-label">输入下划线格式文本 (例如: more_free_fruit)</label>
        <textarea id="snake-input" placeholder="请输入下划线格式的文本..."></textarea>
      </div>
      <div class="button-group">
        <button id="to-camel-btn" class="convert-btn">转换为大驼峰</button>
        <button id="clear-snake-btn" class="clear-btn">清空</button>
      </div>
      <div class="result-container" id="camel-result"></div>
    </div>

    <div class="converter-section">
      <h2 class="converter-title" id="camel-to-snake-title">大驼峰转下划线</h2>
      <div class="input-group">
        <label for="camel-input" id="camel-to-snake-label">输入大驼峰格式文本 (例如: MoreFreeFruit)</label>
        <textarea id="camel-input" placeholder="请输入大驼峰格式的文本..."></textarea>
      </div>
      <div class="button-group">
        <button id="to-snake-btn" class="convert-btn">转换为下划线</button>
        <button id="clear-camel-btn" class="clear-btn">清空</button>
      </div>
      <div class="result-container" id="snake-result"></div>
    </div>
  </div>

  <footer class="footer">
    <div class="footer-content">
      <p>© 2025 Unix Timestamp Converter - A useful tool for developers and system administrators.</p>
      <div class="contact-email">
        <a href="mailto:<EMAIL>" class="contact-link">
          <span class="contact-icon">✉️</span> <EMAIL>
          <button class="copy-email-btn" title="Copy email address" data-tooltip="Copy email" data-email="<EMAIL>">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
              <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
            </svg>
          </button>
        </a>
      </div>
    </div>
  </footer>

  <script>
    // Wait for DOM to be fully loaded
    document.addEventListener('DOMContentLoaded', function() {
      // Cache DOM elements
      const snakeInput = document.getElementById('snake-input');
      const camelInput = document.getElementById('camel-input');
      const camelResult = document.getElementById('camel-result');
      const snakeResult = document.getElementById('snake-result');
      const toCamelBtn = document.getElementById('to-camel-btn');
      const toSnakeBtn = document.getElementById('to-snake-btn');
      const clearSnakeBtn = document.getElementById('clear-snake-btn');
      const clearCamelBtn = document.getElementById('clear-camel-btn');
      const languageBtn = document.querySelector('.language-btn');
      const languageDropdown = document.querySelector('.language-dropdown');

      // Function to convert snake_case to PascalCase
      function snakeToCamel(text) {
        if (typeof text !== 'string') return '';
        return text.split('\n').map(line => {
          if (!line.trim()) return line;
          return line.split(' ').map(part => {
            if (!part.trim()) return part;
            return part
              .split('_')
              .filter(word => word) // Remove empty strings from multiple underscores
              .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
              .join('');
          }).join(' ');
        }).join('\n');
      }

      // Function to convert PascalCase to snake_case
      function camelToSnake(text) {
        if (typeof text !== 'string') return '';
        return text.split('\n').map(line => {
          if (!line.trim()) return line;
          return line.split(' ').map(part => {
            if (!part.trim()) return part;
            return part
              .replace(/([a-z])([A-Z])/g, '$1_$2')
              .toLowerCase()
              .replace(/^_+|_+$/g, ''); // Trim underscores from start/end
          }).join(' ');
        }).join('\n');
      }

      // Update result display
      function updateResult(element, result) {
        if (!element) return;
        element.textContent = result || '';
        element.style.whiteSpace = 'pre-wrap';
        
        // Set ARIA live region attributes for screen readers
        element.setAttribute('aria-live', 'polite');
        element.setAttribute('aria-atomic', 'true');
      }

      // Event handlers
      function handleSnakeToCamel() {
        try {
          const result = snakeToCamel(snakeInput.value);
          updateResult(camelResult, result);
        } catch (error) {
          console.error('Error converting snake_case to PascalCase:', error);
          updateResult(camelResult, 'Error: Could not convert text');
        }
      }

      function handleCamelToSnake() {
        try {
          const result = camelToSnake(camelInput.value);
          updateResult(snakeResult, result);
        } catch (error) {
          console.error('Error converting PascalCase to snake_case:', error);
          updateResult(snakeResult, 'Error: Could not convert text');
        }
      }

      function clearFields(inputElement, resultElement) {
        if (inputElement) inputElement.value = '';
        if (resultElement) updateResult(resultElement, '');
      }

      // Toggle language dropdown
      function toggleLanguageDropdown() {
        languageDropdown.style.display = languageDropdown.style.display === 'block' ? 'none' : 'block';
      }

      // Close dropdown when clicking outside
      function handleClickOutside(event) {
        if (!languageBtn.contains(event.target) && !languageDropdown.contains(event.target)) {
          languageDropdown.style.display = 'none';
        }
      }

      // Event listeners
      if (toCamelBtn) toCamelBtn.addEventListener('click', handleSnakeToCamel);
      if (toSnakeBtn) toSnakeBtn.addEventListener('click', handleCamelToSnake);
      
      if (clearSnakeBtn) clearSnakeBtn.addEventListener('click', () => 
        clearFields(snakeInput, camelResult));
        
      if (clearCamelBtn) clearCamelBtn.addEventListener('click', () => 
        clearFields(camelInput, snakeResult));

      // Language switcher
      if (languageBtn && languageDropdown) {
        languageBtn.addEventListener('click', toggleLanguageDropdown);
        document.addEventListener('click', handleClickOutside);
      }

      // Handle keyboard navigation for the language dropdown
      if (languageBtn) {
        languageBtn.addEventListener('keydown', (e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            toggleLanguageDropdown();
          } else if (e.key === 'Escape') {
            languageDropdown.style.display = 'none';
          }
        });
      }

      // Initialize any empty result areas with proper ARIA attributes
      if (camelResult) updateResult(camelResult, '');
      if (snakeResult) updateResult(snakeResult, '');
    });
  </script>
  <script type="module" src="/script.js"></script>
  <script>
    window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };
  </script>
  <script defer src="/_vercel/insights/script.js"></script>
</body>
</html>