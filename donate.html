<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>支持 Unix 时间戳转换器</title>
  <meta name="description" content="支持 Unix 时间戳转换器的发展。您的捐赠有助于保持此工具免费且无广告。">
  
  <link rel="canonical" href="https://unixtime.help/zh-CN/donate.html">
  <!-- 多语言 hreflang 标签 -->
  <link rel="alternate" hreflang="en" href="https://unixtime.help/donate.html" />
  <link rel="alternate" hreflang="zh-CN" href="https://unixtime.help/zh-CN/donate.html" />
  <link rel="alternate" hreflang="hi-IN" href="https://unixtime.help/hi-IN/donate.html" />
  <link rel="alternate" hreflang="ja-JP" href="https://unixtime.help/ja-JP/donate.html" />
  <link rel="alternate" hreflang="de-DE" href="https://unixtime.help/de-DE/donate.html" />
  <link rel="alternate" hreflang="en-GB" href="https://unixtime.help/en-GB/donate.html" />
  <link rel="alternate" hreflang="ru-RU" href="https://unixtime.help/ru-RU/donate.html" />
  <link rel="alternate" hreflang="ko-KR" href="https://unixtime.help/ko-KR/donate.html" />
  <link rel="alternate" hreflang="en-CA" href="https://unixtime.help/en-CA/donate.html" />
  <link rel="alternate" hreflang="fr-FR" href="https://unixtime.help/fr-FR/donate.html" />
  
  <link rel="icon" href="/icon.png" type="image/png">
  <link rel="stylesheet" href="/style.css">
  <style>
    html, body {
      height: 100%;
      margin: 0;
      padding: 0;
    }
    
    body {
      display: flex;
      flex-direction: column;
      min-height: 100vh;
    }
    
    .donation-container {
      max-width: 800px;
      margin: 2rem auto;
      padding: 2rem;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      flex: 1;
    }
    
    .donation-header {
      text-align: center;
      margin-bottom: 2rem;
    }
    
    .donation-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 2rem;
    }
    
    .donation-qr-container {
      display: flex;
      justify-content: center;
      gap: 2rem;
      flex-wrap: wrap;
      width: 100%;
      margin-bottom: 1.5rem;
    }
    
    .donation-qr {
      text-align: center;
      padding: 1.5rem;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      flex: 1;
      min-width: 200px;
      max-width: 300px;
    }
    
    .donation-qr img {
      max-width: 100%;
      height: auto;
      border: 1px solid #e9ecef;
      border-radius: 4px;
      margin-bottom: 0.5rem;
    }
    
    .donation-qr p {
      margin: 0.5rem 0 0;
      font-weight: 500;
      color: #333;
    }
    
    .donation-message {
      text-align: center;
      line-height: 1.6;
      color: #495057;
    }
    
    .thank-you {
      margin-top: 2rem;
      padding: 1.5rem;
      background: #e8f5e9;
      border-left: 4px solid #4caf50;
      border-radius: 4px;
    }
    
    @media (max-width: 768px) {
      .donation-container {
        padding: 1rem;
        margin: 1rem;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <div class="header-left">
        <h1><a href="/zh-CN/" style="text-decoration: none; color: inherit;">Unix 时间戳转换器</a></h1>
      </div>
      <div class="header-right">
        <a href="/zh-CN/" class="header-btn">首页</a>
      </div>
    </div>

    <div class="donation-container">
      <div class="donation-header">
        <h2>支持我们的工作</h2>
        <p>如果您觉得这个工具有用，请考虑支持我们的开发</p>
      </div>
      
      <div class="donation-content">
        <div class="donation-qr-container">
          <div class="donation-qr">
            <img src="/sweepwx.png" alt="微信支付二维码">
            <p>微信支付</p>
          </div>
          <div class="donation-qr">
            <img src="/sweepzfb.jpg" alt="支付宝二维码">
            <p>支付宝</p>
          </div>
        </div>
        
        <div class="donation-message">
          <p>您的支持有助于保持此工具免费、无广告并持续改进。</p>
          <p>无论金额大小，每一份贡献我们都非常感激，这将帮助我们支付托管和开发费用。</p>
        </div>
        
        <div class="thank-you">
          <h3>感谢您的慷慨支持！❤️</h3>
          <p>您的支持对我们意义重大，有助于保持此工具对所有人开放。</p>
        </div>
      </div>
    </div>
  </div>
  
  <script>
    // 页面初始化代码
    document.addEventListener('DOMContentLoaded', function() {
      // 这里可以添加页面初始化代码
    });
  </script>
  
  <footer class="footer" style="margin-top: auto;">
    <div class="footer-content">
      <p> 2025 Unix Timestamp Converter - 一个对开发者和系统管理员有用的工具</p>
      <div class="contact-email">
        <a href="mailto:<EMAIL>" class="contact-link">
          <span class="contact-icon">✉️</span> 联系开发者: <EMAIL>
          <button class="copy-email-btn" title="复制邮箱地址" data-tooltip="复制邮箱" data-email="<EMAIL>">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
              <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
            </svg>
          </button>
        </a>
      </div>
    </div>
  </footer>
  
  <script>
    window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };
  </script>
  <script defer src="/_vercel/insights/script.js"></script>
</body>
</html>
