// Language translations
const translations = {
    'en-US': {
    seoTitle: 'Unix Timestamp Converter - Free Online Tool for Developers',
    seoDescription: 'Free online Unix timestamp converter. Convert between Unix timestamps and human-readable dates. Supports seconds, milliseconds, microseconds, and nanoseconds.',
    seoKeywords: 'Unix timestamp, timestamp converter, epoch converter, date converter, developer tools',
        pageTitle: 'Unix Timestamp Converter',
        currentSecondsLabel: 'Current Timestamp (s)',
        currentMillisecondsLabel: 'Current Timestamp (ms)',
        currentTimeLabel: 'Current Time',
        copyBtn: 'Copy',
        copied: 'Copied',
        timestampConversionTitle: 'Timestamp Conversion',
        timestampConversionDesc: 'Supports seconds, milliseconds, microseconds and nanoseconds',
        timestampInputPlaceholder: 'Enter timestamp',
        formatLabel: 'Format',
        gmtLabel: 'GMT',
        localTimezoneLabel: 'Local Timezone',
        relativeTimeLabel: 'Relative Time',
        dateConversionTitle: 'Date Conversion',
        dateInputPlaceholder: 'YYYY-MM-DD HH:mm:ss',
        secondsLabel: 'Timestamp (s)',
        millisecondsLabel: 'Timestamp (ms)',
        dateGmtLabel: 'GMT',
        dateLocalLabel: 'Local Timezone',
        dateRelativeLabel: 'Relative Time',
        aboutTitle: 'About Unix Timestamp',
        whatIsUnixTitle: 'What is Unix Timestamp?',
        whatIsUnixDesc: 'Unix timestamp is an integer representing the number of seconds elapsed since January 1, 1970 00:00:00 UTC (the Unix Epoch).',
        timeRangeTitle: 'Time Range',
        timeRangeStart: '- Start time: January 1, 1970 00:00:00 UTC, timestamp: 0',
        timeRangeEnd: '- End time: January 19, 2038 03:14:07 UTC, timestamp: 2,147,483,647',
        timeRangeNote: '* Note: This limitation is based on 32-bit systems. 64-bit systems can represent ±292,277,026,596 years.',
        commonUnitsTitle: 'Common Units',
        unitSeconds: 'Seconds: Most commonly used, 10 digits',
        unitMilliseconds: 'Milliseconds: 1/1000 of a second, 13 digits',
        unitMicroseconds: 'Microseconds: 1/1,000,000 of a second, 16 digits',
        unitNanoseconds: 'Nanoseconds: 1/1,000,000,000 of a second, 19 digits',
        whyUseTitle: 'Why Use Timestamps?',
        whyUse1: 'Unified standard: Not affected by time zones',
        whyUse2: 'Easy calculation: Can be directly compared',
        whyUse3: 'Storage efficient: Represents complete date and time with a single number',
        whyUse4: 'Cross-platform: Supported by all mainstream programming languages',
        y2038Title: 'Year 2038 Problem',
        y2038Desc: 'On 32-bit systems, Unix timestamp will reach its maximum value of 2,147,483,647 on January 19, 2038 03:14:07 UTC, potentially causing overflow issues. Modern 64-bit systems are not affected by this limitation.',
        footerText: '© 2025 Unix Timestamp Converter - A useful tool for developers and system administrators.',
        formatSeconds: 'Seconds (10 digits)',
        formatMilliseconds: 'Milliseconds (13 digits)',
        formatMicroseconds: 'Microseconds (16 digits)',
        formatNanoseconds: 'Nanoseconds (19 digits)',
        formatError: 'Invalid timestamp format',
        futurePrefix: 'Future',
        pastPrefix: 'Past',
        yearsUnit: 'years',
        monthsUnit: 'months',
        daysUnit: 'days',
        hoursUnit: 'hours',
        minutesUnit: 'minutes',
        secondsUnit: 'seconds',
        // New dropdown menu translations
        oneMinLater: '1 min later',
        threeMinLater: '3 min later',
        fiveMinLater: '5 min later',
        customMinLater: 'min later',
        customCopyBtn: 'Copy',
        customCopied: 'Copied!',
        enterNumber: 'Enter #',
        customPlaceholder: 'Custom',
        pcHint: '💡 Tip: Double-click to copy 3min later timestamp',
        mobileHint: '💡 Tip: Double-click to copy 3min later timestamp',
        featuresBtn: '✨ Features',
        caseConverterBtn: '🔤 Case Converter',
        toolsBtn: '🔧 Other Tools',
        donateBtn: '❤️ Donate',
        dateInputHint: 'Select date and time',
        // Donate page translations
        donateTitle: 'Support Our Work',
        donateDesc: 'If you find this tool useful, please consider supporting our development',
        wechatPay: 'WeChat Pay',
        alipay: 'Alipay',
        donateMessage1: 'Your support helps keep this tool free, ad-free, and continuously improving.',
        donateMessage2: 'Every contribution, no matter the size, is greatly appreciated and helps us cover hosting and development costs.',
        thankYouTitle: 'Thank you for your generous support! ❤️',
        thankYouMessage: 'Your support means a lot to us and helps keep this tool open for everyone.',
        // Case converter page translations
        caseConverterTitle: 'Case Converter Tool',
        snakeToCamelTitle: 'Snake Case to Pascal Case',
        snakeToCamelLabel: 'Enter snake_case text (e.g., more_free_fruit)',
        snakeToCamelPlaceholder: 'Please enter snake_case text...',
        convertToCamelBtn: 'Convert to Pascal Case',
        clearBtn: 'Clear',
        camelToSnakeTitle: 'Pascal Case to Snake Case',
        camelToSnakeLabel: 'Enter PascalCase text (e.g., MoreFreeFruit)',
        camelToSnakePlaceholder: 'Please enter PascalCase text...',
        convertToSnakeBtn: 'Convert to Snake Case'
    },
    'zh-CN': {
    seoTitle: 'Unix 时间戳转换器 - 免费在线工具',
    seoDescription: '免费在线Unix时间戳转换工具，支持秒、毫秒、微秒和纳秒的转换，适合开发者和系统管理员。',
    seoKeywords: 'Unix时间戳, 时间戳转换, epoch转换, 日期转换, 开发者工具',
        pageTitle: 'Unix 时间戳转换器',
        currentSecondsLabel: '当前时间戳（秒）',
        currentMillisecondsLabel: '当前时间戳（毫秒）',
        currentTimeLabel: '当前时间',
        copyBtn: '复制',
        copied: '已复制',
        timestampConversionTitle: '时间戳转换',
        timestampConversionDesc: '支持秒、毫秒、微秒和纳秒的时间戳',
        timestampInputPlaceholder: '输入时间戳',
        formatLabel: '格式',
        gmtLabel: '格林威治标准时间',
        localTimezoneLabel: '本地时区',
        relativeTimeLabel: '相对当前时间',
        dateConversionTitle: '日期转换',
        dateInputPlaceholder: 'YYYY-MM-DD HH:mm:ss / 年-月-日 时:分:秒',
        secondsLabel: '时间戳（秒）',
        millisecondsLabel: '时间戳（毫秒）',
        dateGmtLabel: '格林威治标准时间',
        dateLocalLabel: '本地时区',
        dateRelativeLabel: '相对当前时间',
        aboutTitle: '关于 Unix 时间戳',
        whatIsUnixTitle: '什么是 Unix 时间戳?',
        whatIsUnixDesc: 'Unix 时间戳是一个整数，表示自 1970 年 1 月 1 日 00:00:00 UTC（协调世界时）以来经过的秒数，称为 Unix 纪元或 POSIX 时间。',
        timeRangeTitle: '时间范围',
        timeRangeStart: '- 开始时间: 1970年1月1日 00:00:00 UTC，时间戳: 0',
        timeRangeEnd: '- 结束时间: 2038年1月19日 03:14:07 UTC，时间戳: 2,147,483,647',
        timeRangeNote: '* 注意: 在基于32位系统的限制，64位系统可表示范围为±292,277,026,596年',
        commonUnitsTitle: '常用单位',
        unitSeconds: '秒: 最常用单位，10位数字',
        unitMilliseconds: '毫秒: 千分之一秒，13位数字',
        unitMicroseconds: '微秒: 百万分之一秒，16位数字',
        unitNanoseconds: '纳秒: 十亿分之一秒，19位数字',
        whyUseTitle: '为何使用时间戳',
        whyUse1: '统一标准: 不受时区影响',
        whyUse2: '易计算: 可直接比较大小',
        whyUse3: '存储高效: 用单个数字表示完整日期时间',
        whyUse4: '跨平台: 所有主流编程语言均均支持',
        y2038Title: '2038年问题',
        y2038Desc: '在32位系统上，Unix时间戳将在2038年1月19日03:14:07 UTC达到最大值2,147,483,647，可能导致溢出问题，现代64位系统不受此限制影响。',
        footerText: '© 2025 Unix 时间戳转换器 - 为开发者和系统管理员提供的实用工具。',
        formatSeconds: '秒（10位数字）',
        formatMilliseconds: '毫秒（13位数字）',
        formatMicroseconds: '微秒（16位数字）',
        formatNanoseconds: '纳秒（19位数字）',
        formatError: '无效的时间戳格式',
        futurePrefix: '未来',
        pastPrefix: '过去',
        yearsUnit: '年',
        monthsUnit: '个月',
        daysUnit: '天',
        hoursUnit: '小时',
        minutesUnit: '分钟',
        secondsUnit: '秒',
        // New dropdown menu translations
        oneMinLater: '1分钟后',
        threeMinLater: '3分钟后',
        fiveMinLater: '5分钟后',
        customMinLater: '分钟后',
        customCopyBtn: '复制',
        customCopied: '已复制！',
        enterNumber: '输入数字',
        customPlaceholder: '自定义',
        pcHint: '💡 提示：双击复制3分钟后时间戳',
        mobileHint: '💡 提示：双击复制3分钟后时间戳',
        featuresBtn: '✨ 功能特性',
        caseConverterBtn: '🔤 大小写转换',
        toolsBtn: '🔧 其它工具',
        donateBtn: '❤️ 捐赠',
        dateInputHint: '选择日期和时间',
        // Donate page translations
        donateTitle: '支持我们的工作',
        donateDesc: '如果您觉得这个工具有用，请考虑支持我们的开发',
        wechatPay: '微信支付',
        alipay: '支付宝',
        donateMessage1: '您的支持有助于保持此工具免费、无广告并持续改进。',
        donateMessage2: '无论金额大小，每一份贡献我们都非常感激，这将帮助我们支付托管和开发费用。',
        thankYouTitle: '感谢您的慷慨支持！❤️',
        thankYouMessage: '您的支持对我们意义重大，有助于保持此工具对所有人开放。',
        // Case converter page translations
        caseConverterTitle: '大小写转换工具',
        snakeToCamelTitle: '下划线转大驼峰',
        snakeToCamelLabel: '输入下划线格式文本 (例如: more_free_fruit)',
        snakeToCamelPlaceholder: '请输入下划线格式的文本...',
        convertToCamelBtn: '转换为大驼峰',
        clearBtn: '清空',
        camelToSnakeTitle: '大驼峰转下划线',
        camelToSnakeLabel: '输入大驼峰格式文本 (例如: MoreFreeFruit)',
        camelToSnakePlaceholder: '请输入大驼峰格式的文本...',
        convertToSnakeBtn: '转换为下划线'
    },
    'hi-IN': {
    seoTitle: 'यूनिक्स टाइमस्टैम्प कनवर्टर - निशुल्क ऑनलाइन टूल',
    seoDescription: 'नि:शुल्क ऑनलाइन यूनिक्स टाइमस्टैम्प कनवर्टर, सेकंड, मिलीसेकंड, माइक्रोसेकंड और नैनोसेकंड के लिए समर्थन।',
    seoKeywords: 'Unix टाइमस्टैम्प, टाइमस्टैम्प कनवर्टर, epoch कनवर्टर, दिनांक कनवर्टर, डेवलपर टूल',
        pageTitle: 'यूनिक्स टाइमस्टैम्प कनवर्टर',
        currentSecondsLabel: 'वर्तमान टाइमस्टैम्प (सेकंड)',
        currentMillisecondsLabel: 'वर्तमान टाइमस्टैम्प (मिलीसेकंड)',
        currentTimeLabel: 'वर्तमान समय',
        copyBtn: 'कॉपी करें',
        copied: 'कॉपी किया गया',
        timestampConversionTitle: 'टाइमस्टैम्प रूपांतरण',
        timestampConversionDesc: 'सेकंड, मिलीसेकंड, माइक्रोसेकंड और नैनोसेकंड का समर्थन करता है',
        timestampInputPlaceholder: 'टाइमस्टैम्प दर्ज करें',
        formatLabel: 'प्रारूप',
        gmtLabel: 'जीएमटी',
        localTimezoneLabel: 'स्थानीय समय क्षेत्र',
        relativeTimeLabel: 'सापेक्ष समय',
        dateConversionTitle: 'दिनांक रूपांतरण',
        dateInputPlaceholder: 'YYYY-MM-DD HH:mm:ss',
        secondsLabel: 'टाइमस्टैम्प (सेकंड)',
        millisecondsLabel: 'टाइमस्टैम्प (मिलीसेकंड)',
        dateGmtLabel: 'जीएमटी',
        dateLocalLabel: 'स्थानीय समय क्षेत्र',
        dateRelativeLabel: 'सापेक्ष समय',
        aboutTitle: 'यूनिक्स टाइमस्टैम्प के बारे में',
        whatIsUnixTitle: 'यूनिक्स टाइमस्टैम्प क्या है?',
        whatIsUnixDesc: 'यूनिक्स टाइमस्टैम्प एक पूर्णांक है जो 1 जनवरी, 1970 00:00:00 UTC (यूनिक्स एपोक) के बाद से बीते सेकंडों की संख्या का प्रतिनिधित्व करता है।',
        timeRangeTitle: 'समय सीमा',
        timeRangeStart: '- प्रारंभ समय: 1 जनवरी, 1970 00:00:00 UTC, टाइमस्टैम्प: 0',
        timeRangeEnd: '- अंतिम समय: 19 जनवरी, 2038 03:14:07 UTC, टाइमस्टैम्प: 2,147,483,647',
        timeRangeNote: '* नोट: यह सीमा 32-बिट सिस्टम पर आधारित है। 64-बिट सिस्टम ±292,277,026,596 वर्षों का प्रतिनिधित्व कर सकते हैं।',
        commonUnitsTitle: 'सामान्य इकाइयाँ',
        unitSeconds: 'सेकंड: सबसे अधिक उपयोग किया जाता है, 10 अंक',
        unitMilliseconds: 'मिलीसेकंड: 1/1000 सेकंड, 13 अंक',
        unitMicroseconds: 'माइक्रोसेकंड: 1/1,000,000 सेकंड, 16 अंक',
        unitNanoseconds: 'नैनोसेकंड: 1/1,000,000,000 सेकंड, 19 अंक',
        whyUseTitle: 'टाइमस्टैम्प का उपयोग क्यों करें?',
        whyUse1: 'एकीकृत मानक: समय क्षेत्रों से प्रभावित नहीं',
        whyUse2: 'आसान गणना: सीधे तुलना की जा सकती है',
        whyUse3: 'स्टोरेज कुशल: एक ही संख्या से पूरी तारीख और समय का प्रतिनिधित्व करता है',
        whyUse4: 'क्रॉस-प्लेटफॉर्म: सभी मुख्यधारा की प्रोग्रामिंग भाषाओं द्वारा समर्थित',
        y2038Title: 'वर्ष 2038 की समस्या',
        y2038Desc: '32-बिट सिस्टम पर, यूनिक्स टाइमस्टैम्प 19 जनवरी, 2038 03:14:07 UTC पर अपने अधिकतम मान 2,147,483,647 तक पहुंच जाएगा, जिससे ओवरफ्लो समस्याएं हो सकती हैं। आधुनिक 64-बिट सिस्टम इस सीमा से प्रभावित नहीं हैं।',
        footerText: '© 2025 यूनिक्स टाइमस्टैम्प कनवर्टर - डेवलपर्स और सिस्टम प्रशासकों के लिए एक उपयोगी उपकरण।',
        formatSeconds: 'सेकंड (10 अंक)',
        formatMilliseconds: 'मिलीसेकंड (13 अंक)',
        formatMicroseconds: 'माइक्रोसेकंड (16 अंक)',
        formatNanoseconds: 'नैनोसेकंड (19 अंक)',
        formatError: 'अमान्य टाइमस्टैम्प प्रारूप',
        futurePrefix: 'भविष्य',
        pastPrefix: 'पिछला',
        yearsUnit: 'साल',
        monthsUnit: 'महीने',
        daysUnit: 'दिन',
        hoursUnit: 'घंटे',
        minutesUnit: 'मिनट',
        secondsUnit: 'सेकंड',
        // New dropdown menu translations
        oneMinLater: '1 मिनट बाद',
        threeMinLater: '3 मिनट बाद',
        fiveMinLater: '5 मिनट बाद',
        customMinLater: 'मिनट बाद',
        customCopyBtn: 'कॉपी करें',
        customCopied: 'कॉपी हो गया!',
        enterNumber: 'संख्या दर्ज करें',
        customPlaceholder: 'कस्टम',
        pcHint: '💡 सुझाव: 3 मिनट बाद टाइमस्टैम्प कॉपी करने के लिए डबल-क्लिक करें',
        mobileHint: '💡 सुझाव: 3 मिनट बाद टाइमस्टैम्प कॉपी करने के लिए डबल-क्लिक करें',
        featuresBtn: '✨ विशेषताएं',
        caseConverterBtn: '🔤 केस कनवर्टर',
        toolsBtn: '🔧 अन्य उपकरण',
        donateBtn: '❤️ दान करें',
        dateInputHint: 'दिनांक और समय चुनें',
        // Donate page translations
        donateTitle: 'हमारे काम का समर्थन करें',
        donateDesc: 'यदि आपको यह उपकरण उपयोगी लगता है, तो कृपया हमारे विकास का समर्थन करने पर विचार करें',
        wechatPay: 'WeChat Pay',
        alipay: 'Alipay',
        donateMessage1: 'आपका समर्थन इस उपकरण को मुफ्त, विज्ञापन-मुक्त और निरंतर सुधार में मदद करता है।',
        donateMessage2: 'आकार की परवाह किए बिना, हर योगदान की हम बहुत सराहना करते हैं और यह हमें होस्टिंग और विकास लागत को कवर करने में मदद करता है।',
        thankYouTitle: 'आपके उदार समर्थन के लिए धन्यवाद! ❤️',
        thankYouMessage: 'आपका समर्थन हमारे लिए बहुत मायने रखता है और इस उपकरण को सभी के लिए खुला रखने में मदद करता है।',
        // Case converter page translations
        caseConverterTitle: 'केस कनवर्टर उपकरण',
        snakeToCamelTitle: 'Snake Case से Pascal Case',
        snakeToCamelLabel: 'snake_case टेक्स्ट दर्ज करें (जैसे: more_free_fruit)',
        snakeToCamelPlaceholder: 'कृपया snake_case टेक्स्ट दर्ज करें...',
        convertToCamelBtn: 'Pascal Case में बदलें',
        clearBtn: 'साफ़ करें',
        camelToSnakeTitle: 'Pascal Case से Snake Case',
        camelToSnakeLabel: 'PascalCase टेक्स्ट दर्ज करें (जैसे: MoreFreeFruit)',
        camelToSnakePlaceholder: 'कृपया PascalCase टेक्स्ट दर्ज करें...',
        convertToSnakeBtn: 'Snake Case में बदलें'
    },
    'ja-JP': {
    seoTitle: 'Unixタイムスタンプコンバーター - 無料オンラインツール',
    seoDescription: '無料オンラインUnixタイムスタンプコンバーター。秒、ミリ秒、マイクロ秒、ナノ秒の変換をサポート。',
    seoKeywords: 'Unixタイムスタンプ, タイムスタンプ変換, epoch変換, 日付変換, 開発者ツール',
        pageTitle: 'Unixタイムスタンプコンバーター',
        currentSecondsLabel: '現在のタイムスタンプ（秒）',
        currentMillisecondsLabel: '現在のタイムスタンプ（ミリ秒）',
        currentTimeLabel: '現在時刻',
        copyBtn: 'コピー',
        copied: 'コピー済み',
        timestampConversionTitle: 'タイムスタンプ変換',
        timestampConversionDesc: '秒、ミリ秒、マイクロ秒、ナノ秒をサポート',
        timestampInputPlaceholder: 'タイムスタンプを入力',
        formatLabel: 'フォーマット',
        gmtLabel: 'GMT',
        localTimezoneLabel: 'ローカルタイムゾーン',
        relativeTimeLabel: '相対時間',
        dateConversionTitle: '日付変換',
        dateInputPlaceholder: 'YYYY-MM-DD HH:mm:ss',
        secondsLabel: 'タイムスタンプ（秒）',
        millisecondsLabel: 'タイムスタンプ（ミリ秒）',
        dateGmtLabel: 'GMT',
        dateLocalLabel: 'ローカルタイムゾーン',
        dateRelativeLabel: '相対時間',
        aboutTitle: 'Unixタイムスタンプについて',
        whatIsUnixTitle: 'Unixタイムスタンプとは？',
        whatIsUnixDesc: 'Unixタイムスタンプは、1970年1月1日 00:00:00 UTC（Unix紀元）以降の経過秒数を表す整数です。',
        timeRangeTitle: '時間範囲',
        timeRangeStart: '- 開始時間: 1970年1月1日 00:00:00 UTC、タイムスタンプ: 0',
        timeRangeEnd: '- 終了時間: 2038年1月19日 03:14:07 UTC、タイムスタンプ: 2,147,483,647',
        timeRangeNote: '* 注意: この制限は32ビットシステムに基づいています。64ビットシステムでは±292,277,026,596年を表現できます。',
        commonUnitsTitle: '一般的な単位',
        unitSeconds: '秒: 最も一般的に使用される、10桁',
        unitMilliseconds: 'ミリ秒: 1/1000秒、13桁',
        unitMicroseconds: 'マイクロ秒: 1/1,000,000秒、16桁',
        unitNanoseconds: 'ナノ秒: 1/1,000,000,000秒、19桁',
        whyUseTitle: 'タイムスタンプを使用する理由',
        whyUse1: '統一規格: タイムゾーンの影響を受けない',
        whyUse2: '計算が容易: 直接比較可能',
        whyUse3: 'ストレージ効率: 単一の数字で完全な日付と時刻を表現',
        whyUse4: 'クロスプラットフォーム: すべての主要なプログラミング言語でサポート',
        y2038Title: '2038年問題',
        y2038Desc: '32ビットシステムでは、Unixタイムスタンプは2038年1月19日03:14:07 UTCに最大値2,147,483,647に達し、オーバーフロー問題を引き起こす可能性があります。現代の64ビットシステムはこの制限の影響を受けません。',
        footerText: '© 2025 Unixタイムスタンプコンバーター - 開発者とシステム管理者のための便利なツール。',
        formatSeconds: '秒（10桁）',
        formatMilliseconds: 'ミリ秒（13桁）',
        formatMicroseconds: 'マイクロ秒（16桁）',
        formatNanoseconds: 'ナノ秒（19桁）',
        formatError: '無効なタイムスタンプ形式',
        futurePrefix: '未来',
        pastPrefix: '過去',
        yearsUnit: '年',
        monthsUnit: 'ヶ月',
        daysUnit: '日',
        hoursUnit: '時間',
        minutesUnit: '分',
        secondsUnit: '秒',
        // New dropdown menu translations
        oneMinLater: '1分後',
        threeMinLater: '3分後',
        fiveMinLater: '5分後',
        customMinLater: '分後',
        customCopyBtn: 'コピー',
        customCopied: 'コピー済み！',
        enterNumber: '数字を入力',
        customPlaceholder: 'カスタム',
        pcHint: '💡 ヒント：ダブルクリックで3分後のタイムスタンプをコピー',
        mobileHint: '💡 ヒント：ダブルクリックで3分後のタイムスタンプをコピー',
        featuresBtn: '✨ 機能',
        caseConverterBtn: '🔤 ケース変換',
        toolsBtn: '🔧 その他のツール',
        donateBtn: '❤️ 寄付',
        dateInputHint: '日付と時刻を選択',
        // Donate page translations
        donateTitle: '私たちの活動をサポート',
        donateDesc: 'このツールが役に立つと思われる場合は、開発をサポートすることをご検討ください',
        wechatPay: 'WeChat Pay',
        alipay: 'Alipay',
        donateMessage1: 'あなたのサポートにより、このツールを無料で広告なしで継続的に改善することができます。',
        donateMessage2: '金額の大小に関わらず、すべての貢献に感謝し、ホスティングと開発費用をカバーするのに役立ちます。',
        thankYouTitle: 'ご寛大なサポートをありがとうございます！❤️',
        thankYouMessage: 'あなたのサポートは私たちにとって大きな意味があり、このツールをすべての人に開放し続けるのに役立ちます。',
        // Case converter page translations
        caseConverterTitle: 'ケース変換ツール',
        snakeToCamelTitle: 'スネークケースからパスカルケース',
        snakeToCamelLabel: 'snake_caseテキストを入力 (例: more_free_fruit)',
        snakeToCamelPlaceholder: 'snake_caseテキストを入力してください...',
        convertToCamelBtn: 'パスカルケースに変換',
        clearBtn: 'クリア',
        camelToSnakeTitle: 'パスカルケースからスネークケース',
        camelToSnakeLabel: 'PascalCaseテキストを入力 (例: MoreFreeFruit)',
        camelToSnakePlaceholder: 'PascalCaseテキストを入力してください...',
        convertToSnakeBtn: 'スネークケースに変換'
    },
    'de-DE': {
    seoTitle: 'Unix-Zeitstempel-Konverter - Kostenloses Online-Tool',
    seoDescription: 'Kostenloser Unix-Zeitstempel-Konverter online. Unterstützt Sekunden, Millisekunden, Mikrosekunden und Nanosekunden.',
    seoKeywords: 'Unix Zeitstempel, Zeitstempel Konverter, Epoch Konverter, Datum Konverter, Entwickler Tools',
        pageTitle: 'Unix-Zeitstempel-Konverter',
        currentSecondsLabel: 'Aktueller Zeitstempel (s)',
        currentMillisecondsLabel: 'Aktueller Zeitstempel (ms)',
        currentTimeLabel: 'Aktuelle Zeit',
        copyBtn: 'Kopieren',
        copied: 'Kopiert',
        timestampConversionTitle: 'Zeitstempel-Konvertierung',
        timestampConversionDesc: 'Unterstützt Sekunden, Millisekunden, Mikrosekunden und Nanosekunden',
        timestampInputPlaceholder: 'Zeitstempel eingeben',
        formatLabel: 'Format',
        gmtLabel: 'GMT',
        localTimezoneLabel: 'Lokale Zeitzone',
        relativeTimeLabel: 'Relative Zeit',
        dateConversionTitle: 'Datumskonvertierung',
        dateInputPlaceholder: 'JJJJ-MM-TT HH:mm:ss',
        secondsLabel: 'Zeitstempel (s)',
        millisecondsLabel: 'Zeitstempel (ms)',
        dateGmtLabel: 'GMT',
        dateLocalLabel: 'Lokale Zeitzone',
        dateRelativeLabel: 'Relative Zeit',
        aboutTitle: 'Über Unix-Zeitstempel',
        whatIsUnixTitle: 'Was ist ein Unix-Zeitstempel?',
        whatIsUnixDesc: 'Ein Unix-Zeitstempel ist eine Ganzzahl, die die Anzahl der Sekunden darstellt, die seit dem 1. Januar 1970 00:00:00 UTC (der Unix-Epoche) vergangen sind.',
        timeRangeTitle: 'Zeitbereich',
        timeRangeStart: '- Startzeit: 1. Januar 1970 00:00:00 UTC, Zeitstempel: 0',
        timeRangeEnd: '- Endzeit: 19. Januar 2038 03:14:07 UTC, Zeitstempel: 2.147.483.647',
        timeRangeNote: '* Hinweis: Diese Einschränkung basiert auf 32-Bit-Systemen. 64-Bit-Systeme können ±292.277.026.596 Jahre darstellen.',
        commonUnitsTitle: 'Häufig verwendete Einheiten',
        unitSeconds: 'Sekunden: Am häufigsten verwendet, 10 Stellen',
        unitMilliseconds: 'Millisekunden: 1/1000 einer Sekunde, 13 Stellen',
        unitMicroseconds: 'Mikrosekunden: 1/1.000.000 einer Sekunde, 16 Stellen',
        unitNanoseconds: 'Nanosekunden: 1/1.000.000.000 einer Sekunde, 19 Stellen',
        whyUseTitle: 'Warum Zeitstempel verwenden?',
        whyUse1: 'Einheitlicher Standard: Nicht von Zeitzonen beeinflusst',
        whyUse2: 'Einfache Berechnung: Kann direkt verglichen werden',
        whyUse3: 'Speichereffizient: Stellt vollständiges Datum und Uhrzeit mit einer einzigen Zahl dar',
        whyUse4: 'Plattformübergreifend: Von allen gängigen Programmiersprachen unterstützt',
        y2038Title: 'Jahr-2038-Problem',
        y2038Desc: 'Auf 32-Bit-Systemen wird der Unix-Zeitstempel am 19. Januar 2038 03:14:07 UTC seinen Maximalwert von 2.147.483.647 erreichen, was zu Überlaufproblemen führen kann. Moderne 64-Bit-Systeme sind von dieser Einschränkung nicht betroffen.',
        footerText: '© 2025 Unix-Zeitstempel-Konverter - Ein nützliches Tool für Entwickler und Systemadministratoren.',
        formatSeconds: 'Sekunden (10 Stellen)',
        formatMilliseconds: 'Millisekunden (13 Stellen)',
        formatMicroseconds: 'Mikrosekunden (16 Stellen)',
        formatNanoseconds: 'Nanosekunden (19 Stellen)',
        formatError: 'Ungültiges Zeitstempelformat',
        futurePrefix: 'Zukunft',
        pastPrefix: 'Vergangenheit',
        yearsUnit: 'Jahre',
        monthsUnit: 'Monate',
        daysUnit: 'Tage',
        hoursUnit: 'Stunden',
        minutesUnit: 'Minuten',
        secondsUnit: 'Sekunden',
        // New dropdown menu translations
        oneMinLater: '1 Min später',
        threeMinLater: '3 Min später',
        fiveMinLater: '5 Min später',
        customMinLater: 'Min später',
        customCopyBtn: 'Kopieren',
        customCopied: 'Kopiert!',
        enterNumber: 'Zahl eingeben',
        customPlaceholder: 'Benutzerdefiniert',
        pcHint: '💡 Tipp: Doppelklick zum Kopieren des 3-Minuten-späteren Zeitstempels',
        mobileHint: '💡 Tipp: Doppelklick zum Kopieren des 3-Minuten-späteren Zeitstempels',
        featuresBtn: '✨ Funktionen',
        caseConverterBtn: '🔤 Groß-/Kleinschreibung',
        toolsBtn: '🔧 Weitere Tools',
        donateBtn: '❤️ Spenden',
        dateInputHint: 'Datum und Uhrzeit auswählen',
        // Donate page translations
        donateTitle: 'Unterstützen Sie unsere Arbeit',
        donateDesc: 'Wenn Sie dieses Tool nützlich finden, unterstützen Sie bitte unsere Entwicklung',
        wechatPay: 'WeChat Pay',
        alipay: 'Alipay',
        donateMessage1: 'Ihre Unterstützung hilft dabei, dieses Tool kostenlos, werbefrei und kontinuierlich zu verbessern.',
        donateMessage2: 'Jeder Beitrag, egal wie groß, wird sehr geschätzt und hilft uns, Hosting- und Entwicklungskosten zu decken.',
        thankYouTitle: 'Vielen Dank für Ihre großzügige Unterstützung! ❤️',
        thankYouMessage: 'Ihre Unterstützung bedeutet uns viel und hilft dabei, dieses Tool für alle offen zu halten.',
        // Case converter page translations
        caseConverterTitle: 'Groß-/Kleinschreibung Konverter',
        snakeToCamelTitle: 'Snake Case zu Pascal Case',
        snakeToCamelLabel: 'Snake_case Text eingeben (z.B.: more_free_fruit)',
        snakeToCamelPlaceholder: 'Bitte geben Sie snake_case Text ein...',
        convertToCamelBtn: 'Zu Pascal Case konvertieren',
        clearBtn: 'Löschen',
        camelToSnakeTitle: 'Pascal Case zu Snake Case',
        camelToSnakeLabel: 'PascalCase Text eingeben (z.B.: MoreFreeFruit)',
        camelToSnakePlaceholder: 'Bitte geben Sie PascalCase Text ein...',
        convertToSnakeBtn: 'Zu Snake Case konvertieren'
    },
    'en-GB': {
    seoTitle: 'Unix Timestamp Converter - Free Online Tool',
    seoDescription: 'Free online Unix timestamp converter. Convert between Unix timestamps and human-readable dates. Supports seconds, milliseconds, microseconds, and nanoseconds.',
    seoKeywords: 'Unix timestamp, timestamp converter, epoch converter, date converter, developer tools',
        pageTitle: 'Unix Timestamp Converter',
        currentSecondsLabel: 'Current Timestamp (s)',
        currentMillisecondsLabel: 'Current Timestamp (ms)',
        currentTimeLabel: 'Current Time',
        copyBtn: 'Copy',
        copied: 'Copied',
        timestampConversionTitle: 'Timestamp Conversion',
        timestampConversionDesc: 'Supports seconds, milliseconds, microseconds and nanoseconds',
        timestampInputPlaceholder: 'Enter timestamp',
        formatLabel: 'Format',
        gmtLabel: 'GMT',
        localTimezoneLabel: 'Local Timezone',
        relativeTimeLabel: 'Relative Time',
        dateConversionTitle: 'Date Conversion',
        dateInputPlaceholder: 'YYYY-MM-DD HH:mm:ss',
        secondsLabel: 'Timestamp (s)',
        millisecondsLabel: 'Timestamp (ms)',
        dateGmtLabel: 'GMT',
        dateLocalLabel: 'Local Timezone',
        dateRelativeLabel: 'Relative Time',
        aboutTitle: 'About Unix Timestamp',
        whatIsUnixTitle: 'What is Unix Timestamp?',
        whatIsUnixDesc: 'Unix timestamp is an integer representing the number of seconds elapsed since 1 January 1970 00:00:00 UTC (the Unix Epoch).',
        timeRangeTitle: 'Time Range',
        timeRangeStart: '- Start time: 1 January 1970 00:00:00 UTC, timestamp: 0',
        timeRangeEnd: '- End time: 19 January 2038 03:14:07 UTC, timestamp: 2,147,483,647',
        timeRangeNote: '* Note: This limitation is based on 32-bit systems. 64-bit systems can represent ±292,277,026,596 years.',
        commonUnitsTitle: 'Common Units',
        unitSeconds: 'Seconds: Most commonly used, 10 digits',
        unitMilliseconds: 'Milliseconds: 1/1000 of a second, 13 digits',
        unitMicroseconds: 'Microseconds: 1/1,000,000 of a second, 16 digits',
        unitNanoseconds: 'Nanoseconds: 1/1,000,000,000 of a second, 19 digits',
        whyUseTitle: 'Why Use Timestamps?',
        whyUse1: 'Unified standard: Not affected by time zones',
        whyUse2: 'Easy calculation: Can be directly compared',
        whyUse3: 'Storage efficient: Represents complete date and time with a single number',
        whyUse4: 'Cross-platform: Supported by all mainstream programming languages',
        y2038Title: 'Year 2038 Problem',
        y2038Desc: 'On 32-bit systems, Unix timestamp will reach its maximum value of 2,147,483,647 on 19 January 2038 03:14:07 UTC, potentially causing overflow issues. Modern 64-bit systems are not affected by this limitation.',
        footerText: '© 2025 Unix Timestamp Converter - A useful tool for developers and system administrators.',
        formatSeconds: 'Seconds (10 digits)',
        formatMilliseconds: 'Milliseconds (13 digits)',
        formatMicroseconds: 'Microseconds (16 digits)',
        formatNanoseconds: 'Nanoseconds (19 digits)',
        formatError: 'Invalid timestamp format',
        futurePrefix: 'Future',
        pastPrefix: 'Past',
        yearsUnit: 'years',
        monthsUnit: 'months',
        daysUnit: 'days',
        hoursUnit: 'hours',
        minutesUnit: 'minutes',
        secondsUnit: 'seconds',
        // New dropdown menu translations
        oneMinLater: '1 min later',
        threeMinLater: '3 min later',
        fiveMinLater: '5 min later',
        customMinLater: 'min later',
        customCopyBtn: 'Copy',
        customCopied: 'Copied!',
        enterNumber: 'Enter #',
        customPlaceholder: 'Custom',
        pcHint: '💡 Tip: Double-click to copy 3min later timestamp',
        mobileHint: '💡 Tip: Double-click to copy 3min later timestamp',
        featuresBtn: '✨ Features',
        caseConverterBtn: '🔤 Case Converter',
        toolsBtn: '🔧 Other Tools',
        donateBtn: '❤️ Donate',
        dateInputHint: 'Select date and time',
        // Donate page translations
        donateTitle: 'Support Our Work',
        donateDesc: 'If you find this tool useful, please consider supporting our development',
        wechatPay: 'WeChat Pay',
        alipay: 'Alipay',
        donateMessage1: 'Your support helps keep this tool free, ad-free, and continuously improving.',
        donateMessage2: 'Every contribution, no matter the size, is greatly appreciated and helps us cover hosting and development costs.',
        thankYouTitle: 'Thank you for your generous support! ❤️',
        thankYouMessage: 'Your support means a lot to us and helps keep this tool open for everyone.',
        // Case converter page translations
        caseConverterTitle: 'Case Converter Tool',
        snakeToCamelTitle: 'Snake Case to Pascal Case',
        snakeToCamelLabel: 'Enter snake_case text (e.g., more_free_fruit)',
        snakeToCamelPlaceholder: 'Please enter snake_case text...',
        convertToCamelBtn: 'Convert to Pascal Case',
        clearBtn: 'Clear',
        camelToSnakeTitle: 'Pascal Case to Snake Case',
        camelToSnakeLabel: 'Enter PascalCase text (e.g., MoreFreeFruit)',
        camelToSnakePlaceholder: 'Please enter PascalCase text...',
        convertToSnakeBtn: 'Convert to Snake Case'
    },
    'ru-RU': {
    seoTitle: 'Конвертер Unix-времени - Бесплатный онлайн-инструмент',
    seoDescription: 'Бесплатный онлайн-конвертер Unix-времени. Поддержка секунд, миллисекунд, микросекунд и наносекунд.',
    seoKeywords: 'Unix время, конвертер времени, epoch конвертер, конвертер дат, инструменты разработчика',
        pageTitle: 'Конвертер временных меток Unix',
        currentSecondsLabel: 'Текущая метка времени (с)',
        currentMillisecondsLabel: 'Текущая метка времени (мс)',
        currentTimeLabel: 'Текущее время',
        copyBtn: 'Копировать',
        copied: 'Скопировано',
        timestampConversionTitle: 'Преобразование временной метки',
        timestampConversionDesc: 'Поддерживает секунды, миллисекунды, микросекунды и наносекунды',
        timestampInputPlaceholder: 'Введите временную метку',
        formatLabel: 'Формат',
        gmtLabel: 'GMT',
        localTimezoneLabel: 'Местный часовой пояс',
        relativeTimeLabel: 'Относительное время',
        dateConversionTitle: 'Преобразование даты',
        dateInputPlaceholder: 'ГГГГ-ММ-ДД ЧЧ:мм:сс',
        secondsLabel: 'Временная метка (с)',
        millisecondsLabel: 'Временная метка (мс)',
        dateGmtLabel: 'GMT',
        dateLocalLabel: 'Местный часовой пояс',
        dateRelativeLabel: 'Относительное время',
        aboutTitle: 'О временных метках Unix',
        whatIsUnixTitle: 'Что такое временная метка Unix?',
        whatIsUnixDesc: 'Временная метка Unix - это целое число, представляющее количество секунд, прошедших с 1 января 1970 года 00:00:00 UTC (эпоха Unix).',
        timeRangeTitle: 'Временной диапазон',
        timeRangeStart: '- Время начала: 1 января 1970 года 00:00:00 UTC, временная метка: 0',
        timeRangeEnd: '- Время окончания: 19 января 2038 года 03:14:07 UTC, временная метка: 2,147,483,647',
        timeRangeNote: '* Примечание: Это ограничение основано на 32-битных системах. 64-битные системы могут представлять ±292,277,026,596 лет.',
        commonUnitsTitle: 'Общие единицы',
        unitSeconds: 'Секунды: Наиболее часто используемые, 10 цифр',
        unitMilliseconds: 'Миллисекунды: 1/1000 секунды, 13 цифр',
        unitMicroseconds: 'Микросекунды: 1/1,000,000 секунды, 16 цифр',
        unitNanoseconds: 'Наносекунды: 1/1,000,000,000 секунды, 19 цифр',
        whyUseTitle: 'Почему используют временные метки?',
        whyUse1: 'Единый стандарт: Не зависит от часовых поясов',
        whyUse2: 'Простой расчет: Можно напрямую сравнивать',
        whyUse3: 'Эффективное хранение: Представляет полную дату и время одним числом',
        whyUse4: 'Кроссплатформенность: Поддерживается всеми основными языками программирования',
        y2038Title: 'Проблема 2038 года',
        y2038Desc: 'На 32-битных системах временная метка Unix достигнет своего максимального значения 2,147,483,647 19 января 2038 года 03:14:07 UTC, что потенциально может вызвать проблемы переполнения. Современные 64-битные системы не подвержены этому ограничению.',
        footerText: '© 2025 Конвертер временных меток Unix - Полезный инструмент для разработчиков и системных администраторов.',
        formatSeconds: 'Секунды (10 цифр)',
        formatMilliseconds: 'Миллисекунды (13 цифр)',
        formatMicroseconds: 'Микросекунды (16 цифр)',
        formatNanoseconds: 'Наносекунды (19 цифр)',
        formatError: 'Недействительный формат временной метки',
        futurePrefix: 'Будущее',
        pastPrefix: 'Прошлое',
        yearsUnit: 'лет',
        monthsUnit: 'месяцев',
        daysUnit: 'дней',
        hoursUnit: 'часов',
        minutesUnit: 'минут',
        secondsUnit: 'секунд',
        // New dropdown menu translations
        oneMinLater: '1 мин позже',
        threeMinLater: '3 мин позже',
        fiveMinLater: '5 мин позже',
        customMinLater: 'мин позже',
        customCopyBtn: 'Копировать',
        customCopied: 'Скопировано!',
        enterNumber: 'Введите #',
        customPlaceholder: 'Пользовательский',
        pcHint: '💡 Совет: Дважды щелкните для копирования временной метки через 3 минуты',
        mobileHint: '💡 Совет: Дважды щелкните для копирования временной метки через 3 минуты',
        featuresBtn: '✨ Функции',
        caseConverterBtn: '🔤 Конвертер регистра',
        toolsBtn: '🔧 Другие инструменты',
        donateBtn: '❤️ Пожертвовать',
        dateInputHint: 'Выберите дату и время',
        // Donate page translations
        donateTitle: 'Поддержите нашу работу',
        donateDesc: 'Если вы считаете этот инструмент полезным, пожалуйста, рассмотрите возможность поддержки нашей разработки',
        wechatPay: 'WeChat Pay',
        alipay: 'Alipay',
        donateMessage1: 'Ваша поддержка помогает сохранить этот инструмент бесплатным, без рекламы и постоянно улучшающимся.',
        donateMessage2: 'Каждый вклад, независимо от размера, очень ценится и помогает нам покрывать расходы на хостинг и разработку.',
        thankYouTitle: 'Спасибо за вашу щедрую поддержку! ❤️',
        thankYouMessage: 'Ваша поддержка много значит для нас и помогает сохранить этот инструмент открытым для всех.',
        // Case converter page translations
        caseConverterTitle: 'Инструмент конвертера регистра',
        snakeToCamelTitle: 'Snake Case в Pascal Case',
        snakeToCamelLabel: 'Введите текст snake_case (например: more_free_fruit)',
        snakeToCamelPlaceholder: 'Пожалуйста, введите текст snake_case...',
        convertToCamelBtn: 'Конвертировать в Pascal Case',
        clearBtn: 'Очистить',
        camelToSnakeTitle: 'Pascal Case в Snake Case',
        camelToSnakeLabel: 'Введите текст PascalCase (например: MoreFreeFruit)',
        camelToSnakePlaceholder: 'Пожалуйста, введите текст PascalCase...',
        convertToSnakeBtn: 'Конвертировать в Snake Case'
    },
    'ko-KR': {
    seoTitle: 'Unix 타임스탬프 변환기 - 무료 온라인 도구',
    seoDescription: '무료 온라인 Unix 타임스탬프 변환기. 초, 밀리초, 마이크로초, 나노초 지원.',
    seoKeywords: 'Unix 타임스탬프, 타임스탬프 변환, epoch 변환, 날짜 변환, 개발자 도구',
        pageTitle: 'Unix 타임스탬프 변환기',
        currentSecondsLabel: '현재 타임스탬프 (초)',
        currentMillisecondsLabel: '현재 타임스탬프 (밀리초)',
        currentTimeLabel: '현재 시간',
        copyBtn: '복사',
        copied: '복사됨',
        timestampConversionTitle: '타임스탬프 변환',
        timestampConversionDesc: '초, 밀리초, 마이크로초 및 나노초 지원',
        timestampInputPlaceholder: '타임스탬프 입력',
        formatLabel: '형식',
        gmtLabel: 'GMT',
        localTimezoneLabel: '현지 시간대',
        relativeTimeLabel: '상대 시간',
        dateConversionTitle: '날짜 변환',
        dateInputPlaceholder: 'YYYY-MM-DD HH:mm:ss',
        secondsLabel: '타임스탬프 (초)',
        millisecondsLabel: '타임스탬프 (밀리초)',
        dateGmtLabel: 'GMT',
        dateLocalLabel: '현지 시간대',
        dateRelativeLabel: '상대 시간',
        aboutTitle: 'Unix 타임스탬프 소개',
        whatIsUnixTitle: 'Unix 타임스탬프란?',
        whatIsUnixDesc: 'Unix 타임스탬프는 1970년 1월 1일 00:00:00 UTC(Unix 에포크) 이후 경과된 초 수를 나타내는 정수입니다.',
        timeRangeTitle: '시간 범위',
        timeRangeStart: '- 시작 시간: 1970년 1월 1일 00:00:00 UTC, 타임스탬프: 0',
        timeRangeEnd: '- 종료 시간: 2038년 1월 19일 03:14:07 UTC, 타임스탬프: 2,147,483,647',
        timeRangeNote: '* 참고: 이 제한은 32비트 시스템을 기반으로 합니다. 64비트 시스템은 ±292,277,026,596년을 표현할 수 있습니다.',
        commonUnitsTitle: '일반 단위',
        unitSeconds: '초: 가장 일반적으로 사용됨, 10자리',
        unitMilliseconds: '밀리초: 1/1000초, 13자리',
        unitMicroseconds: '마이크로초: 1/1,000,000초, 16자리',
        unitNanoseconds: '나노초: 1/1,000,000,000초, 19자리',
        whyUseTitle: '타임스탬프를 사용하는 이유?',
        whyUse1: '통일된 표준: 시간대의 영향을 받지 않음',
        whyUse2: '쉬운 계산: 직접 비교 가능',
        whyUse3: '저장 효율성: 단일 숫자로 전체 날짜와 시간 표현',
        whyUse4: '크로스 플랫폼: 모든 주요 프로그래밍 언어에서 지원',
        y2038Title: '2038년 문제',
        y2038Desc: '32비트 시스템에서 Unix 타임스탬프는 2038년 1월 19일 03:14:07 UTC에 최대값 2,147,483,647에 도달하여 오버플로우 문제를 일으킬 수 있습니다. 현대의 64비트 시스템은 이 제한의 영향을 받지 않습니다.',
        footerText: '© 2025 Unix 타임스탬프 변환기 - 개발자 및 시스템 관리자를 위한 유용한 도구.',
        formatSeconds: '초 (10자리)',
        formatMilliseconds: '밀리초 (13자리)',
        formatMicroseconds: '마이크로초 (16자리)',
        formatNanoseconds: '나노초 (19자리)',
        formatError: '잘못된 타임스탬프 형식',
        futurePrefix: '미래',
        pastPrefix: '과거',
        yearsUnit: '년',
        monthsUnit: '개월',
        daysUnit: '일',
        hoursUnit: '시간',
        minutesUnit: '분',
        secondsUnit: '초',
        // New dropdown menu translations
        oneMinLater: '1분 후',
        threeMinLater: '3분 후',
        fiveMinLater: '5분 후',
        customMinLater: '분 후',
        customCopyBtn: '복사',
        customCopied: '복사됨!',
        enterNumber: '숫자 입력',
        customPlaceholder: '사용자 정의',
        pcHint: '💡 팁: 더블클릭으로 3분 후 타임스탬프 복사',
        mobileHint: '💡 팁: 더블클릭으로 3분 후 타임스탬프 복사',
        featuresBtn: '✨ 기능',
        caseConverterBtn: '🔤 대소문자 변환',
        toolsBtn: '🔧 기타 도구',
        donateBtn: '❤️ 기부',
        dateInputHint: '날짜와 시간 선택',
        // Donate page translations
        donateTitle: '우리의 작업을 지원해주세요',
        donateDesc: '이 도구가 유용하다고 생각하시면 개발을 지원해주시기 바랍니다',
        wechatPay: 'WeChat Pay',
        alipay: 'Alipay',
        donateMessage1: '여러분의 지원은 이 도구를 무료로, 광고 없이, 지속적으로 개선하는 데 도움이 됩니다.',
        donateMessage2: '크기에 관계없이 모든 기여는 매우 감사하며 호스팅 및 개발 비용을 충당하는 데 도움이 됩니다.',
        thankYouTitle: '여러분의 관대한 지원에 감사드립니다! ❤️',
        thankYouMessage: '여러분의 지원은 우리에게 큰 의미가 있으며 이 도구를 모든 사람에게 열어두는 데 도움이 됩니다.',
        // Case converter page translations
        caseConverterTitle: '대소문자 변환 도구',
        snakeToCamelTitle: 'Snake Case에서 Pascal Case로',
        snakeToCamelLabel: 'snake_case 텍스트 입력 (예: more_free_fruit)',
        snakeToCamelPlaceholder: 'snake_case 텍스트를 입력해주세요...',
        convertToCamelBtn: 'Pascal Case로 변환',
        clearBtn: '지우기',
        camelToSnakeTitle: 'Pascal Case에서 Snake Case로',
        camelToSnakeLabel: 'PascalCase 텍스트 입력 (예: MoreFreeFruit)',
        camelToSnakePlaceholder: 'PascalCase 텍스트를 입력해주세요...',
        convertToSnakeBtn: 'Snake Case로 변환'
    },
    'en-CA': {
    seoTitle: 'Unix Timestamp Converter - Free Online Tool for Developers',
    seoDescription: 'Free online Unix timestamp converter. Convert between Unix timestamps and human-readable dates. Supports seconds, milliseconds, microseconds, and nanoseconds.',
    seoKeywords: 'Unix timestamp, timestamp converter, epoch converter, date converter, developer tools',
        pageTitle: 'Unix Timestamp Converter',
        currentSecondsLabel: 'Current Timestamp (s)',
        currentMillisecondsLabel: 'Current Timestamp (ms)',
        currentTimeLabel: 'Current Time',
        copyBtn: 'Copy',
        copied: 'Copied',
        timestampConversionTitle: 'Timestamp Conversion',
        timestampConversionDesc: 'Supports seconds, milliseconds, microseconds and nanoseconds',
        timestampInputPlaceholder: 'Enter timestamp',
        formatLabel: 'Format',
        gmtLabel: 'GMT',
        localTimezoneLabel: 'Local Timezone',
        relativeTimeLabel: 'Relative Time',
        dateConversionTitle: 'Date Conversion',
        dateInputPlaceholder: 'YYYY-MM-DD HH:mm:ss',
        secondsLabel: 'Timestamp (s)',
        millisecondsLabel: 'Timestamp (ms)',
        dateGmtLabel: 'GMT',
        dateLocalLabel: 'Local Timezone',
        dateRelativeLabel: 'Relative Time',
        aboutTitle: 'About Unix Timestamp',
        whatIsUnixTitle: 'What is Unix Timestamp?',
        whatIsUnixDesc: 'Unix timestamp is an integer representing the number of seconds elapsed since January 1, 1970 00:00:00 UTC (the Unix Epoch).',
        timeRangeTitle: 'Time Range',
        timeRangeStart: '- Start time: January 1, 1970 00:00:00 UTC, timestamp: 0',
        timeRangeEnd: '- End time: January 19, 2038 03:14:07 UTC, timestamp: 2,147,483,647',
        timeRangeNote: '* Note: This limitation is based on 32-bit systems. 64-bit systems can represent ±292,277,026,596 years.',
        commonUnitsTitle: 'Common Units',
        unitSeconds: 'Seconds: Most commonly used, 10 digits',
        unitMilliseconds: 'Milliseconds: 1/1000 of a second, 13 digits',
        unitMicroseconds: 'Microseconds: 1/1,000,000 of a second, 16 digits',
        unitNanoseconds: 'Nanoseconds: 1/1,000,000,000 of a second, 19 digits',
        whyUseTitle: 'Why Use Timestamps?',
        whyUse1: 'Unified standard: Not affected by time zones',
        whyUse2: 'Easy calculation: Can be directly compared',
        whyUse3: 'Storage efficient: Represents complete date and time with a single number',
        whyUse4: 'Cross-platform: Supported by all mainstream programming languages',
        y2038Title: 'Year 2038 Problem',
        y2038Desc: 'On 32-bit systems, Unix timestamp will reach its maximum value of 2,147,483,647 on January 19, 2038 03:14:07 UTC, potentially causing overflow issues. Modern 64-bit systems are not affected by this limitation.',
        footerText: '© 2025 Unix Timestamp Converter - A useful tool for developers and system administrators.',
        formatSeconds: 'Seconds (10 digits)',
        formatMilliseconds: 'Milliseconds (13 digits)',
        formatMicroseconds: 'Microseconds (16 digits)',
        formatNanoseconds: 'Nanoseconds (19 digits)',
        formatError: 'Invalid timestamp format',
        futurePrefix: 'Future',
        pastPrefix: 'Past',
        yearsUnit: 'years',
        monthsUnit: 'months',
        daysUnit: 'days',
        hoursUnit: 'hours',
        minutesUnit: 'minutes',
        secondsUnit: 'seconds',
        // New dropdown menu translations
        oneMinLater: '1 min later',
        threeMinLater: '3 min later',
        fiveMinLater: '5 min later',
        customMinLater: 'min later',
        customCopyBtn: 'Copy',
        customCopied: 'Copied!',
        enterNumber: 'Enter #',
        customPlaceholder: 'Custom',
        pcHint: '💡 Tip: Double-click to copy 3min later timestamp',
        mobileHint: '💡 Tip: Double-click to copy 3min later timestamp',
        featuresBtn: '✨ Features',
        caseConverterBtn: '🔤 Case Converter',
        toolsBtn: '🔧 Other Tools',
        donateBtn: '❤️ Donate',
        dateInputHint: 'Select date and time',
        // Donate page translations
        donateTitle: 'Support Our Work',
        donateDesc: 'If you find this tool useful, please consider supporting our development',
        wechatPay: 'WeChat Pay',
        alipay: 'Alipay',
        donateMessage1: 'Your support helps keep this tool free, ad-free, and continuously improving.',
        donateMessage2: 'Every contribution, no matter the size, is greatly appreciated and helps us cover hosting and development costs.',
        thankYouTitle: 'Thank you for your generous support! ❤️',
        thankYouMessage: 'Your support means a lot to us and helps keep this tool open for everyone.',
        // Case converter page translations
        caseConverterTitle: 'Case Converter Tool',
        snakeToCamelTitle: 'Snake Case to Pascal Case',
        snakeToCamelLabel: 'Enter snake_case text (e.g., more_free_fruit)',
        snakeToCamelPlaceholder: 'Please enter snake_case text...',
        convertToCamelBtn: 'Convert to Pascal Case',
        clearBtn: 'Clear',
        camelToSnakeTitle: 'Pascal Case to Snake Case',
        camelToSnakeLabel: 'Enter PascalCase text (e.g., MoreFreeFruit)',
        camelToSnakePlaceholder: 'Please enter PascalCase text...',
        convertToSnakeBtn: 'Convert to Snake Case'
    },
    'fr-FR': {
    seoTitle: 'Convertisseur de Timestamp Unix - Outil en ligne gratuit',
    seoDescription: 'Outil gratuit de conversion de timestamp Unix en ligne. Prend en charge les secondes, millisecondes, microsecondes et nanosecondes.',
    seoKeywords: 'Unix timestamp, convertisseur timestamp, convertisseur epoch, convertisseur date, outil développeur',
        pageTitle: 'Convertisseur de Timestamp Unix',
        currentSecondsLabel: 'Timestamp actuel (s)',
        currentMillisecondsLabel: 'Timestamp actuel (ms)',
        currentTimeLabel: 'Heure actuelle',
        copyBtn: 'Copier',
        copied: 'Copié',
        timestampConversionTitle: 'Conversion de timestamp',
        timestampConversionDesc: 'Prend en charge les secondes, millisecondes, microsecondes et nanosecondes',
        timestampInputPlaceholder: 'Entrez un timestamp',
        formatLabel: 'Format',
        gmtLabel: 'GMT',
        localTimezoneLabel: 'Fuseau horaire local',
        relativeTimeLabel: 'Temps relatif',
        dateConversionTitle: 'Conversion de date',
        dateInputPlaceholder: 'AAAA-MM-JJ HH:mm:ss',
        secondsLabel: 'Timestamp (s)',
        millisecondsLabel: 'Timestamp (ms)',
        dateGmtLabel: 'GMT',
        dateLocalLabel: 'Fuseau horaire local',
        dateRelativeLabel: 'Temps relatif',
        aboutTitle: 'À propos du timestamp Unix',
        whatIsUnixTitle: 'Qu\'est-ce qu\'un timestamp Unix ?',
        whatIsUnixDesc: 'Le timestamp Unix est un entier représentant le nombre de secondes écoulées depuis le 1er janvier 1970 00:00:00 UTC (l\'époque Unix).',
        timeRangeTitle: 'Plage de temps',
        timeRangeStart: '- Heure de début: 1er janvier 1970 00:00:00 UTC, timestamp: 0',
        timeRangeEnd: '- Heure de fin: 19 janvier 2038 03:14:07 UTC, timestamp: 2 147 483 647',
        timeRangeNote: '* Remarque: Cette limitation est basée sur les systèmes 32 bits. Les systèmes 64 bits peuvent représenter ±292 277 026 596 ans.',
        commonUnitsTitle: 'Unités courantes',
        unitSeconds: 'Secondes: Le plus couramment utilisé, 10 chiffres',
        unitMilliseconds: 'Millisecondes: 1/1000 d\'une seconde, 13 chiffres',
        unitMicroseconds: 'Microsecondes: 1/1 000 000 d\'une seconde, 16 chiffres',
        unitNanoseconds: 'Nanosecondes: 1/1 000 000 000 d\'une seconde, 19 chiffres',
        whyUseTitle: 'Pourquoi utiliser des timestamps ?',
        whyUse1: 'Standard unifié: Non affecté par les fuseaux horaires',
        whyUse2: 'Calcul facile: Peut être directement comparé',
        whyUse3: 'Stockage efficace: Représente une date et une heure complètes avec un seul nombre',
        whyUse4: 'Multi-plateforme: Pris en charge par tous les langages de programmation courants',
        y2038Title: 'Problème de l\'an 2038',
        y2038Desc: 'Sur les systèmes 32 bits, le timestamp Unix atteindra sa valeur maximale de 2 147 483 647 le 19 janvier 2038 03:14:07 UTC, ce qui pourrait causer des problèmes de dépassement. Les systèmes 64 bits modernes ne sont pas affectés par cette limitation.',
        footerText: '© 2025 Convertisseur de Timestamp Unix - Un outil utile pour les développeurs et les administrateurs système.',
        formatSeconds: 'Secondes (10 chiffres)',
        formatMilliseconds: 'Millisecondes (13 chiffres)',
        formatMicroseconds: 'Microsecondes (16 chiffres)',
        formatNanoseconds: 'Nanosecondes (19 chiffres)',
        formatError: 'Format de timestamp invalide',
        futurePrefix: 'Futur',
        pastPrefix: 'Passé',
        yearsUnit: 'ans',
        monthsUnit: 'mois',
        daysUnit: 'jours',
        hoursUnit: 'heures',
        minutesUnit: 'minutes',
        secondsUnit: 'secondes',
        // New dropdown menu translations
        oneMinLater: '1 min plus tard',
        threeMinLater: '3 min plus tard',
        fiveMinLater: '5 min plus tard',
        customMinLater: 'min plus tard',
        customCopyBtn: 'Copier',
        customCopied: 'Copié!',
        enterNumber: 'Entrez #',
        customPlaceholder: 'Personnalisé',
        pcHint: '💡 Astuce: Double-cliquez pour copier le timestamp 3min plus tard',
        mobileHint: '💡 Astuce: Double-cliquez pour copier le timestamp 3min plus tard',
        featuresBtn: '✨ Fonctionnalités',
        caseConverterBtn: '🔤 Convertisseur de casse',
        toolsBtn: '🔧 Autres outils',
        donateBtn: '❤️ Faire un don',
        dateInputHint: 'Sélectionner la date et l\'heure',
        // Donate page translations
        donateTitle: 'Soutenez notre travail',
        donateDesc: 'Si vous trouvez cet outil utile, veuillez considérer soutenir notre développement',
        wechatPay: 'WeChat Pay',
        alipay: 'Alipay',
        donateMessage1: 'Votre soutien aide à garder cet outil gratuit, sans publicité et en amélioration continue.',
        donateMessage2: 'Chaque contribution, quelle que soit sa taille, est grandement appréciée et nous aide à couvrir les coûts d\'hébergement et de développement.',
        thankYouTitle: 'Merci pour votre généreux soutien ! ❤️',
        thankYouMessage: 'Votre soutien signifie beaucoup pour nous et aide à garder cet outil ouvert pour tous.',
        // Case converter page translations
        caseConverterTitle: 'Outil de conversion de casse',
        snakeToCamelTitle: 'Snake Case vers Pascal Case',
        snakeToCamelLabel: 'Entrez le texte snake_case (ex: more_free_fruit)',
        snakeToCamelPlaceholder: 'Veuillez entrer le texte snake_case...',
        convertToCamelBtn: 'Convertir en Pascal Case',
        clearBtn: 'Effacer',
        camelToSnakeTitle: 'Pascal Case vers Snake Case',
        camelToSnakeLabel: 'Entrez le texte PascalCase (ex: MoreFreeFruit)',
        camelToSnakePlaceholder: 'Veuillez entrer le texte PascalCase...',
        convertToSnakeBtn: 'Convertir en Snake Case'
    }
};

// Current language
let currentLang = 'en-US';

// 根据IP自动检测并设置多语言
async function autoDetectLanguageByIP() {
    try {
        // 推荐使用ipapi.co，支持CORS且返回country_code
        const response = await fetch('https://ipapi.co/json/');
        const data = await response.json();
        const countryCode = data.country_code; // 如CN、JP、IN等

        // 国家码与translations变量key的对应关系
        const countryToLang = {
            'CN': 'zh-CN',
            'JP': 'ja-JP',
            'IN': 'hi-IN',
            'DE': 'de-DE',
            'GB': 'en-GB',
            'RU': 'ru-RU',
            'KR': 'ko-KR',
            'CA': 'en-CA',
            'FR': 'fr-FR',
            'US': 'en-US',
            // 可根据需要继续补充
        };
        let detectedLang = countryToLang[countryCode];
        // 若translations中有该语言则使用，否则默认en-US
        if (!detectedLang || !(detectedLang in translations)) {
            detectedLang = 'en-US';
        }
        currentLang = detectedLang;
        updateUIText();
    } catch (e) {
        currentLang = 'en-US';
        updateUIText();
    }
}

if (!localStorage.getItem('lang')) {
    autoDetectLanguageByIP();
} else {
    currentLang = localStorage.getItem('lang');
    updateUIText();
}

// 根据 URL 路径自动切换语言
(function autoSetLangByPath() {
    // Skip this in development environment
    if (isDevelopmentEnvironment()) {
        return;
    }

    const pathLang = window.location.pathname.replace(/^\//, '').replace(/\/$/, '');
    const supportedLangs = ['en-GB','zh-CN','hi-IN','ja-JP','de-DE','ru-RU','ko-KR','en-CA','fr-FR'];

    if (supportedLangs.includes(pathLang)) {
        // Set the language based on URL path
        localStorage.setItem('lang', pathLang);
        currentLang = pathLang;

        // Don't redirect in production, let Vercel handle the rewrite
        // The vercel.json already rewrites these paths to /
        // Just update the UI with the correct language
        updateUIText();
    } else if (window.location.pathname === '/' || window.location.pathname === '') {
        // Root path - just use the saved language preference without redirecting
        const savedLang = localStorage.getItem('lang');
        if (savedLang) {
            currentLang = savedLang;
            updateUIText();
        }
    }
})();

// 动态更新SEO标签
function updateSEOTags() {
    const lang = translations[currentLang];
    if (lang && lang.seoTitle) document.title = lang.seoTitle;
    if (lang && lang.seoDescription) {
        let descMeta = document.querySelector('meta[name="description"]');
        if (descMeta) descMeta.setAttribute('content', lang.seoDescription);
    }
    if (lang && lang.seoKeywords) {
        let kwMeta = document.querySelector('meta[name="keywords"]');
        if (kwMeta) kwMeta.setAttribute('content', lang.seoKeywords);
    }
}

// Update UI text based on language
function updateUIText() {
    updateSEOTags();
    const lang = translations[currentLang];
    if (!lang) return; // Skip if language not found

    // Update page title if element exists
    const pageTitle = document.getElementById('page-title');
    if (pageTitle) {
        pageTitle.textContent = lang.pageTitle;
    }
    document.title = lang.pageTitle || 'Unix Timestamp Converter';

    // Update language button if it exists
    const langBtn = document.getElementById('language-btn');
    const langOption = document.querySelector(`.language-option[data-lang="${currentLang}"]`);
    if (langBtn && langOption) {
        langBtn.textContent = langOption.textContent;
    }

    // Update current time section if elements exist
    const updateIfExists = (id, content) => {
        const el = document.getElementById(id);
        if (el && content) el.textContent = content;
    };
    
    updateIfExists('current-seconds-label', lang.currentSecondsLabel);
    updateIfExists('current-milliseconds-label', lang.currentMillisecondsLabel);
    updateIfExists('current-time-label', lang.currentTimeLabel);
    
    // Update copy buttons if they exist
    const copyButtons = document.querySelectorAll('.copy-btn');
    if (copyButtons.length > 0 && lang.copyBtn) {
        copyButtons.forEach(btn => {
            btn.textContent = lang.copyBtn;
        });
    }

    // Update timestamp conversion section if elements exist
    updateIfExists('timestamp-conversion-title', lang.timestampConversionTitle);
    updateIfExists('timestamp-conversion-desc', lang.timestampConversionDesc);
    
    const timestampInputEl = document.getElementById('timestamp-input');
    if (timestampInputEl && lang.timestampInputPlaceholder) {
        timestampInputEl.placeholder = lang.timestampInputPlaceholder;
    }
    
    updateIfExists('format-label', lang.formatLabel);
    updateIfExists('gmt-label', lang.gmtLabel);
    updateIfExists('local-timezone-label', lang.localTimezoneLabel);
    updateIfExists('relative-time-label', lang.relativeTimeLabel);

    // Update date conversion section if elements exist
    updateIfExists('date-conversion-title', lang.dateConversionTitle);
    updateIfExists('date-conversion-desc', lang.dateConversionDesc);
    
    const dateInputEl = document.getElementById('date-input');
    if (dateInputEl && lang.dateInputPlaceholder) {
        dateInputEl.placeholder = lang.dateInputPlaceholder;
    }
    
    updateIfExists('seconds-label', lang.secondsLabel);
    updateIfExists('milliseconds-label', lang.millisecondsLabel);
    updateIfExists('date-gmt-label', lang.gmtLabel);
    updateIfExists('date-local-label', lang.localTimezoneLabel);
    updateIfExists('date-relative-label', lang.relativeTimeLabel);

    // Update about section
    const aboutToggle = document.querySelector('#about-toggle span');
    if (aboutToggle && lang.aboutTitle) aboutToggle.textContent = lang.aboutTitle;
    
    // Update about section content
    updateIfExists('what-is-unix-title', lang.whatIsUnixTitle);
    updateIfExists('what-is-unix-desc', lang.whatIsUnixDesc);
    updateIfExists('time-range-title', lang.timeRangeTitle);
    updateIfExists('time-range-start', lang.timeRangeStart);
    updateIfExists('time-range-end', lang.timeRangeEnd);
    updateIfExists('time-range-note', lang.timeRangeNote);
    updateIfExists('common-units-title', lang.commonUnitsTitle);
    updateIfExists('unit-seconds', lang.unitSeconds);
    updateIfExists('unit-milliseconds', lang.unitMilliseconds);
    updateIfExists('unit-microseconds', lang.unitMicroseconds);
    updateIfExists('unit-nanoseconds', lang.unitNanoseconds);
    updateIfExists('why-use-title', lang.whyUseTitle);
    updateIfExists('why-use-1', lang.whyUse1);
    updateIfExists('why-use-2', lang.whyUse2);
    updateIfExists('why-use-3', lang.whyUse3);
    updateIfExists('why-use-4', lang.whyUse4);
    updateIfExists('y2038-title', lang.y2038Title);
    updateIfExists('y2038-desc', lang.y2038Desc);

    // Update footer - we now have a more complex footer structure
    const footerTextElement = document.querySelector('.footer-content p');
    if (footerTextElement) {
        footerTextElement.textContent = lang.footerText;
    }

    // Only update dropdown menu texts if we're not on the donation page
    if (!window.location.pathname.includes('donate.html')) {
        updateDropdownTexts(lang);

        // Update any existing format detection text
        const timestampInput = document.getElementById('timestamp-input');
        if (timestampInput && timestampInput.value.trim()) {
            detectAndDisplayTimestampFormat(timestampInput.value.trim());
        }
    }
}

// Helper function to update element text if element exists
function updateIfExists(elementId, text) {
    const element = document.getElementById(elementId);
    if (element && text) {
        element.textContent = text;
    }
}

// Update dropdown menu texts
function updateDropdownTexts(lang) {
    // Update preset time options if they exist
    const types = ['seconds', 'milliseconds', 'datetime'];
    types.forEach(type => {
        // Helper function to safely update element text if it exists
        const updateElementText = (id, text) => {
            const element = document.getElementById(id);
            if (element) element.textContent = text;
        };

        // Update all time option buttons if they exist
        updateElementText(`one-min-later-${type}`, lang.oneMinLater);
        updateElementText(`three-min-later-${type}`, lang.threeMinLater);
        updateElementText(`five-min-later-${type}`, lang.fiveMinLater);
        updateElementText(`custom-min-later-${type}`, lang.customMinLater);
        updateElementText(`custom-copy-btn-${type}`, lang.customCopyBtn);

        // Update input placeholder if it exists
        const input = document.getElementById(`custom-${type}-input`);
        if (input && lang.customPlaceholder) {
            input.placeholder = lang.customPlaceholder;
        }
    });

    // Update PC hint text if it exists
    const pcHintElement = document.getElementById('pc-hint-text');
    if (pcHintElement && lang.pcHint) {
        pcHintElement.textContent = lang.pcHint;
    }

    // Update mobile hint text
    const mobileHintElement = document.getElementById('mobile-hint-text');
    if (mobileHintElement && lang.mobileHint) {
        mobileHintElement.textContent = lang.mobileHint;
    }

    // Update header buttons
    const toolsBtn = document.getElementById('tools-btn');
    if (toolsBtn && lang.toolsBtn) {
        toolsBtn.textContent = lang.toolsBtn;
    }

    const toolsFeatures = document.getElementById('tools-features');
    if (toolsFeatures && lang.featuresBtn) {
        toolsFeatures.textContent = lang.featuresBtn;
    }

    const toolsCaseConverter = document.getElementById('tools-case-converter');
    if (toolsCaseConverter && lang.caseConverterBtn) {
        toolsCaseConverter.textContent = lang.caseConverterBtn;
    }

    const donateBtn = document.getElementById('donate-btn');
    if (donateBtn && lang.donateBtn) {
        donateBtn.textContent = lang.donateBtn;
    }

    // Update date input hint
    const dateInputHint = document.getElementById('date-input-hint');
    if (dateInputHint && lang.dateInputHint) {
        dateInputHint.textContent = lang.dateInputHint;
    }

    // Update donate page elements
    updateIfExists('donate-title', lang.donateTitle);
    updateIfExists('donate-desc', lang.donateDesc);
    updateIfExists('wechat-pay', lang.wechatPay);
    updateIfExists('alipay', lang.alipay);
    updateIfExists('donate-message-1', lang.donateMessage1);
    updateIfExists('donate-message-2', lang.donateMessage2);
    updateIfExists('thank-you-title', lang.thankYouTitle);
    updateIfExists('thank-you-message', lang.thankYouMessage);

    // Update case converter page elements
    updateIfExists('case-converter-title', lang.caseConverterTitle);
    updateIfExists('snake-to-camel-title', lang.snakeToCamelTitle);
    updateIfExists('snake-to-camel-label', lang.snakeToCamelLabel);
    updateIfExists('to-camel-btn', lang.convertToCamelBtn);
    updateIfExists('clear-snake-btn', lang.clearBtn);
    updateIfExists('camel-to-snake-title', lang.camelToSnakeTitle);
    updateIfExists('camel-to-snake-label', lang.camelToSnakeLabel);
    updateIfExists('to-snake-btn', lang.convertToSnakeBtn);
    updateIfExists('clear-camel-btn', lang.clearBtn);

    // Update placeholders
    const snakeInput = document.getElementById('snake-input');
    if (snakeInput && lang.snakeToCamelPlaceholder) {
        snakeInput.placeholder = lang.snakeToCamelPlaceholder;
    }
    const camelInput = document.getElementById('camel-input');
    if (camelInput && lang.camelToSnakePlaceholder) {
        camelInput.placeholder = lang.camelToSnakePlaceholder;
    }
}

// Language dropdown toggle
const languageBtn = document.getElementById('language-btn');
if (languageBtn) {
    languageBtn.addEventListener('click', function () {
        const dropdown = document.getElementById('language-dropdown');
        if (dropdown) {
            dropdown.classList.toggle('show');
        }
    });

    // Close dropdown when clicking outside
    window.addEventListener('click', function (event) {
        if (!event.target.matches('.language-btn')) {
            const dropdown = document.getElementById('language-dropdown');
            if (dropdown && dropdown.classList.contains('show')) {
                dropdown.classList.remove('show');
            }
        }
    });
}

// Tools dropdown toggle
const toolsBtn = document.getElementById('tools-btn');
if (toolsBtn) {
    toolsBtn.addEventListener('click', function () {
        const dropdown = document.getElementById('tools-dropdown');
        if (dropdown) {
            dropdown.classList.toggle('show');
        }
    });

    // Close dropdown when clicking outside
    window.addEventListener('click', function (event) {
        if (!event.target.matches('.tools-btn')) {
            const dropdown = document.getElementById('tools-dropdown');
            if (dropdown && dropdown.classList.contains('show')) {
                dropdown.classList.remove('show');
            }
        }
    });
}

// Check if we're in development environment
function isDevelopmentEnvironment() {
    return window.location.hostname === 'localhost' ||
           window.location.hostname === '127.0.0.1' ||
           window.location.hostname.includes('localhost');
}

// Language selection
document.querySelectorAll('.language-option').forEach(option => {
    option.addEventListener('click', function () {
        const lang = this.getAttribute('data-lang');
        localStorage.setItem('lang', lang);

        if (isDevelopmentEnvironment()) {
            // In development, just update the language without redirecting
            currentLang = lang;
            updateUIText();
            // Close the dropdown
            document.getElementById('language-dropdown').classList.remove('show');
        } else {
            // In production (Vercel), just update the language without redirecting
            // This prevents the 404 error when trying to access /en-US/
            currentLang = lang;
            updateUIText();
            // Close the dropdown
            document.getElementById('language-dropdown').classList.remove('show');
        }
    });
});

// Update current time
function updateCurrentTime() {
    const now = new Date();
    const timestampSeconds = Math.floor(now.getTime() / 1000);
    const timestampMilliseconds = now.getTime();

    // Safely update elements if they exist
    const updateElementText = (id, text) => {
        const el = document.getElementById(id);
        if (el) el.textContent = text;
    };

    updateElementText('current-timestamp-seconds', timestampSeconds);
    updateElementText('current-timestamp-milliseconds', timestampMilliseconds);

    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');
    const formattedDateTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    
    updateElementText('current-datetime', formattedDateTime);
}

// Only initialize time updates on the main page (index.html) or pages that explicitly need it
const isMainPage = window.location.pathname.endsWith('/') ||
                  window.location.pathname.endsWith('index.html') ||
                  window.location.pathname.endsWith('zh-CN/') ||
                  window.location.pathname.endsWith('zh-CN/index.html');

// Enhanced copy button functionality with dropdown
let customMinutesCache = {
    seconds: 10,
    milliseconds: 10,
    datetime: 10
};

// Load cached custom minutes from localStorage
function loadCustomMinutesCache() {
    const cached = localStorage.getItem('customMinutesCache');
    if (cached) {
        try {
            customMinutesCache = { ...customMinutesCache, ...JSON.parse(cached) };
        } catch (e) {
            console.warn('Failed to load custom minutes cache:', e);
        }
    }

    // Set cached values to inputs - with null checks
    const secondsInput = document.getElementById('custom-seconds-input');
    const millisecondsInput = document.getElementById('custom-milliseconds-input');
    const datetimeInput = document.getElementById('custom-datetime-input');

    if (secondsInput) {
        secondsInput.value = customMinutesCache.seconds;
    }
    if (millisecondsInput) {
        millisecondsInput.value = customMinutesCache.milliseconds;
    }
    if (datetimeInput) {
        datetimeInput.value = customMinutesCache.datetime;
    }
}

// Save custom minutes to localStorage
function saveCustomMinutesCache() {
    localStorage.setItem('customMinutesCache', JSON.stringify(customMinutesCache));
}

// Calculate future timestamp/datetime
function calculateFutureTime(type, minutes) {
    const now = new Date();
    const futureTime = new Date(now.getTime() + minutes * 60 * 1000);

    switch (type) {
        case 'seconds':
            return Math.floor(futureTime.getTime() / 1000).toString();
        case 'milliseconds':
            return futureTime.getTime().toString();
        case 'datetime':
            const year = futureTime.getFullYear();
            const month = String(futureTime.getMonth() + 1).padStart(2, '0');
            const day = String(futureTime.getDate()).padStart(2, '0');
            const hours = String(futureTime.getHours()).padStart(2, '0');
            const mins = String(futureTime.getMinutes()).padStart(2, '0');
            const secs = String(futureTime.getSeconds()).padStart(2, '0');
            return `${year}-${month}-${day} ${hours}:${mins}:${secs}`;
        default:
            return '';
    }
}

// Copy text to clipboard with feedback - Enhanced for mobile compatibility
function copyToClipboard(text, button) {
    // Function to show success feedback
    function showSuccessFeedback() {
        // If button is already showing feedback, don't start a new one
        if (button.feedbackTimeout) {
            return;
        }

        // Store original styles and content only once
        if (!button.originalState) {
            button.originalState = {
                content: button.innerHTML,
                backgroundColor: button.style.backgroundColor || '',
                border: button.style.border || '',
                color: button.style.color || '',
                transform: button.style.transform || '',
                transition: button.style.transition || '',
                boxShadow: button.style.boxShadow || ''
            };
        }

        // Show green checkmark with animation
        button.innerHTML = '✓';
        button.style.backgroundColor = '#28a745';
        button.style.border = '1px solid #28a745';
        button.style.color = 'white';
        button.style.transform = 'scale(1.1)';
        button.style.transition = 'all 0.2s ease';
        button.style.boxShadow = '0 2px 8px rgba(40, 167, 69, 0.3)';

        // Set timeout to restore original state
        button.feedbackTimeout = setTimeout(() => {
            button.innerHTML = button.originalState.content;
            button.style.backgroundColor = button.originalState.backgroundColor;
            button.style.border = button.originalState.border;
            button.style.color = button.originalState.color;
            button.style.transform = button.originalState.transform;
            button.style.transition = button.originalState.transition;
            button.style.boxShadow = button.originalState.boxShadow;

            // Clear the timeout reference
            button.feedbackTimeout = null;
        }, 1500);
    }

    // Function to show error feedback
    function showErrorFeedback() {
        const originalText = button.textContent;
        button.textContent = '复制失败';
        setTimeout(() => {
            button.textContent = originalText;
        }, 1500);
    }

    // Method 1: Try modern Clipboard API (works in HTTPS and newer browsers)
    if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(text).then(() => {
            showSuccessFeedback();
        }).catch(err => {
            console.warn('Clipboard API failed, trying fallback:', err);
            // Fallback to legacy method
            fallbackCopyTextToClipboard(text, showSuccessFeedback, showErrorFeedback);
        });
    } else {
        // Fallback for older browsers or HTTP sites
        fallbackCopyTextToClipboard(text, showSuccessFeedback, showErrorFeedback);
    }
}

// Fallback copy method for older browsers and mobile devices
function fallbackCopyTextToClipboard(text, onSuccess, onError) {
    // Create a temporary textarea element
    const textArea = document.createElement('textarea');
    textArea.value = text;

    // Make it invisible but still selectable
    textArea.style.position = 'fixed';
    textArea.style.top = '0';
    textArea.style.left = '0';
    textArea.style.width = '2em';
    textArea.style.height = '2em';
    textArea.style.padding = '0';
    textArea.style.border = 'none';
    textArea.style.outline = 'none';
    textArea.style.boxShadow = 'none';
    textArea.style.background = 'transparent';
    textArea.style.fontSize = '16px'; // Prevent zoom on iOS
    textArea.style.opacity = '0';
    textArea.style.pointerEvents = 'none';

    document.body.appendChild(textArea);

    try {
        // Focus and select the text
        textArea.focus();
        textArea.select();
        textArea.setSelectionRange(0, 99999); // For mobile devices

        // Try to copy using execCommand
        const successful = document.execCommand('copy');

        if (successful) {
            onSuccess();
        } else {
            // Last resort: show the text for manual copy
            showManualCopyDialog(text, onSuccess);
        }
    } catch (err) {
        console.error('Fallback copy failed:', err);
        // Last resort: show the text for manual copy
        showManualCopyDialog(text, onSuccess);
    } finally {
        // Clean up
        document.body.removeChild(textArea);
    }
}

// Manual copy dialog for extreme fallback cases
function showManualCopyDialog(text, onSuccess) {
    // Create a modal dialog for manual copy
    const modal = document.createElement('div');
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10000;
        font-family: Arial, sans-serif;
    `;

    const dialog = document.createElement('div');
    dialog.style.cssText = `
        background: white;
        padding: 20px;
        border-radius: 8px;
        max-width: 90%;
        max-height: 80%;
        overflow: auto;
        box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    `;

    const title = document.createElement('h3');
    title.textContent = '请手动复制以下内容：';
    title.style.marginTop = '0';

    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.cssText = `
        width: 100%;
        height: 100px;
        margin: 10px 0;
        padding: 8px;
        border: 1px solid #ccc;
        border-radius: 4px;
        font-family: monospace;
        font-size: 14px;
        resize: vertical;
    `;
    textArea.readOnly = true;

    const buttonContainer = document.createElement('div');
    buttonContainer.style.textAlign = 'center';

    const closeButton = document.createElement('button');
    closeButton.textContent = '关闭';
    closeButton.style.cssText = `
        padding: 8px 16px;
        margin: 0 5px;
        border: 1px solid #ccc;
        border-radius: 4px;
        background: #f5f5f5;
        cursor: pointer;
    `;

    const selectButton = document.createElement('button');
    selectButton.textContent = '全选';
    selectButton.style.cssText = `
        padding: 8px 16px;
        margin: 0 5px;
        border: 1px solid #007bff;
        border-radius: 4px;
        background: #007bff;
        color: white;
        cursor: pointer;
    `;

    // Event handlers
    closeButton.onclick = () => {
        document.body.removeChild(modal);
        onSuccess(); // Still show success since user saw the content
    };

    selectButton.onclick = () => {
        textArea.select();
        textArea.setSelectionRange(0, 99999);
    };

    modal.onclick = (e) => {
        if (e.target === modal) {
            document.body.removeChild(modal);
            onSuccess();
        }
    };

    // Assemble the dialog
    buttonContainer.appendChild(selectButton);
    buttonContainer.appendChild(closeButton);
    dialog.appendChild(title);
    dialog.appendChild(textArea);
    dialog.appendChild(buttonContainer);
    modal.appendChild(dialog);
    document.body.appendChild(modal);

    // Auto-select the text
    setTimeout(() => {
        textArea.select();
        textArea.setSelectionRange(0, 99999);
    }, 100);
}

// Hide all dropdowns
function hideAllDropdowns() {
    document.querySelectorAll('.copy-dropdown').forEach(dropdown => {
        dropdown.classList.remove('show');
    });
}

// Enhanced copy button functionality with mobile support
document.querySelectorAll('.copy-btn').forEach(button => {
    let clickTimeout;
    let touchStartTime;
    let touchMoved = false;

    // Detect if device supports touch
    const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

    // Mouse/Click events for desktop and mobile
    button.addEventListener('click', function (e) {
        e.stopPropagation();

        // On touch devices, prevent click events from triggering copy
        // Touch events will handle the copy logic
        if (isTouchDevice) {
            e.preventDefault();
            return;
        }

        // Clear any existing timeout
        if (clickTimeout) {
            clearTimeout(clickTimeout);
            clickTimeout = null;
            return; // If there's already a timeout, this might be a double click, so don't set a new timeout
        }

        // Set a timeout to handle single click
        clickTimeout = setTimeout(() => {
            const elementId = this.getAttribute('data-copy');
            const text = document.getElementById(elementId).textContent;
            copyToClipboard(text, this);
            clickTimeout = null; // Clear the timeout reference
        }, 300); // 300ms delay to distinguish from double click
    });

    // Double click to show dropdown (desktop)
    button.addEventListener('dblclick', function (e) {
        e.stopPropagation();

        // Clear the single click timeout
        if (clickTimeout) {
            clearTimeout(clickTimeout);
            clickTimeout = null;
        }

        showDropdown(this);
    });

    // Touch events for mobile - support double tap like desktop
    if (isTouchDevice) {
        let lastTouchTime = 0;
        let touchTimeout = null;

        button.addEventListener('touchstart', function (e) {
            touchStartTime = Date.now();
            touchMoved = false;
        });

        button.addEventListener('touchmove', function (e) {
            touchMoved = true;
        });

        button.addEventListener('touchend', function (e) {
            const touchDuration = Date.now() - touchStartTime;
            const currentTime = Date.now();

            // If it was a short tap and no movement
            if (touchDuration < 300 && !touchMoved) {
                // Check if this is a double tap (within 300ms of last tap)
                if (currentTime - lastTouchTime < 300) {
                    // Double tap detected - show dropdown
                    e.preventDefault();

                    // Clear any pending single tap action
                    if (touchTimeout) {
                        clearTimeout(touchTimeout);
                        touchTimeout = null;
                    }

                    showDropdown(this);

                    // Add haptic feedback if available
                    if (navigator.vibrate) {
                        navigator.vibrate(50);
                    }

                    // Reset last touch time to prevent triple tap
                    lastTouchTime = 0;
                } else {
                    // Potential single tap - wait to see if there's a second tap
                    lastTouchTime = currentTime;

                    // Clear any existing timeout
                    if (touchTimeout) {
                        clearTimeout(touchTimeout);
                    }

                    // Set timeout for single tap action
                    touchTimeout = setTimeout(() => {
                        // This is confirmed as a single tap - trigger copy
                        const elementId = this.getAttribute('data-copy');
                        const text = document.getElementById(elementId).textContent;
                        copyToClipboard(text, this);
                        touchTimeout = null;
                    }, 300);
                }
            }
        });
    }

    // Function to show dropdown
    function showDropdown(buttonElement) {
        const type = buttonElement.getAttribute('data-type');
        const dropdown = document.getElementById(`copy-dropdown-${type}`);

        // Hide other dropdowns first
        hideAllDropdowns();

        // Toggle current dropdown
        dropdown.classList.toggle('show');
    }
});

// Dropdown option click handlers
document.querySelectorAll('.copy-option:not(.custom-option)').forEach(option => {
    option.addEventListener('click', function (e) {
        e.stopPropagation();

        const minutes = parseInt(this.getAttribute('data-minutes'));
        const type = this.getAttribute('data-type');
        const futureTime = calculateFutureTime(type, minutes);

        // Find the corresponding copy button for feedback
        const copyButton = document.getElementById(`copy-${type}-btn`);
        copyToClipboard(futureTime, copyButton);

        // Hide dropdown
        hideAllDropdowns();
    });
});

// Custom minutes input handlers
document.querySelectorAll('.custom-minutes-input').forEach(input => {
    // Handle Enter key and blur events
    function handleCustomMinutes() {
        const minutes = parseInt(input.value);
        if (isNaN(minutes) || minutes < 1) {
            return;
        }

        const type = input.id.replace('custom-', '').replace('-input', '');

        // Update cache
        customMinutesCache[type] = minutes;
        saveCustomMinutesCache();

        const futureTime = calculateFutureTime(type, minutes);

        // Find the corresponding copy button for feedback
        const copyButton = document.getElementById(`copy-${type}-btn`);
        copyToClipboard(futureTime, copyButton);

        // Hide dropdown
        hideAllDropdowns();
    }

    input.addEventListener('keypress', function (e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            handleCustomMinutes();
        }
    });

    input.addEventListener('blur', function () {
        const minutes = parseInt(this.value);
        if (!isNaN(minutes) && minutes >= 1) {
            const type = this.id.replace('custom-', '').replace('-input', '');
            customMinutesCache[type] = minutes;
            saveCustomMinutesCache();
        }
    });

    // Handle input changes for real-time copying
    input.addEventListener('input', function () {
        const minutes = parseInt(this.value);
        if (!isNaN(minutes) && minutes >= 1) {
            const type = this.id.replace('custom-', '').replace('-input', '');

            // Update cache
            customMinutesCache[type] = minutes;
            saveCustomMinutesCache();

            const futureTime = calculateFutureTime(type, minutes);

            // Find the corresponding copy button for feedback
            const copyButton = document.getElementById(`copy-${type}-btn`);
            copyToClipboard(futureTime, copyButton);
        }
    });

    // Prevent dropdown from closing when clicking on input
    input.addEventListener('click', function (e) {
        e.stopPropagation();
    });
});

// Custom copy button handlers
document.querySelectorAll('.custom-copy-btn').forEach(button => {
    button.addEventListener('click', function (e) {
        e.stopPropagation();

        const type = this.getAttribute('data-type');
        const input = document.getElementById(`custom-${type}-input`);
        const minutes = parseInt(input.value);

        if (isNaN(minutes) || minutes < 1) {
            // If no valid input, show a brief message
            const originalText = this.textContent;
            this.textContent = translations[currentLang].enterNumber;
            setTimeout(() => {
                this.textContent = originalText;
            }, 1000);
            return;
        }

        // Update cache
        customMinutesCache[type] = minutes;
        saveCustomMinutesCache();

        const futureTime = calculateFutureTime(type, minutes);

        // Find the corresponding main copy button for feedback
        const mainCopyButton = document.getElementById(`copy-${type}-btn`);
        copyToClipboard(futureTime, mainCopyButton);

        // Also show feedback on this button
        const originalText = this.textContent;
        this.textContent = translations[currentLang].customCopied;
        setTimeout(() => {
            this.textContent = originalText;
        }, 1500);

        // Hide dropdown after a short delay
        setTimeout(() => {
            hideAllDropdowns();
        }, 1000);
    });

    // Prevent dropdown from closing when clicking on button
    button.addEventListener('click', function (e) {
        e.stopPropagation();
    });
});

// Close dropdowns when clicking outside
document.addEventListener('click', function (e) {
    if (!e.target.closest('.copy-btn-container')) {
        hideAllDropdowns();
    }
});

// Prevent dropdown from closing when clicking inside it
document.querySelectorAll('.copy-dropdown').forEach(dropdown => {
    dropdown.addEventListener('click', function (e) {
        e.stopPropagation();
    });
});

// Detect timestamp format
function detectTimestampFormat(timestamp) {
    const num = parseInt(timestamp, 10);
    if (isNaN(num)) return null;

    const now = Date.now();
    const length = timestamp.length;

    // Check if the timestamp is a valid date when interpreted as seconds
    const dateFromSeconds = new Date(num * 1000);
    const isValidSecondTimestamp = dateFromSeconds.getTime() > 0 &&
        dateFromSeconds.getFullYear() >= 1970 &&
        dateFromSeconds.getFullYear() <= 2100;

    // Check if the timestamp is a valid date when interpreted as milliseconds
    const dateFromMilliseconds = new Date(num);
    const isValidMillisecondTimestamp = dateFromMilliseconds.getTime() > 0 &&
        dateFromMilliseconds.getFullYear() >= 1970 &&
        dateFromMilliseconds.getFullYear() <= 2100;

    if (length <= 10 && isValidSecondTimestamp) {
        return {type: 'seconds', value: num * 1000};
    } else if (length <= 13 && isValidMillisecondTimestamp) {
        return {type: 'milliseconds', value: num};
    } else if (length <= 16) {
        // Microseconds: convert to milliseconds by dividing by 1000
        return {type: 'microseconds', value: Math.floor(num / 1000)};
    } else if (length <= 19) {
        // Nanoseconds: convert to milliseconds by dividing by 1000000
        return {type: 'nanoseconds', value: Math.floor(num / 1000000)};
    }

    return null;
}

// Display detected format with appropriate styling
function detectAndDisplayTimestampFormat(timestamp) {
    const formatElement = document.getElementById('timestamp-format');
    const detected = detectTimestampFormat(timestamp);

    if (!detected) {
        formatElement.textContent = translations[currentLang].formatError;
        formatElement.className = 'format-error';
        return null;
    }

    let formatText = '';
    switch (detected.type) {
        case 'seconds':
            formatText = translations[currentLang].formatSeconds;
            break;
        case 'milliseconds':
            formatText = translations[currentLang].formatMilliseconds;
            break;
        case 'microseconds':
            formatText = translations[currentLang].formatMicroseconds;
            break;
        case 'nanoseconds':
            formatText = translations[currentLang].formatNanoseconds;
            break;
    }

    formatElement.textContent = formatText;
    formatElement.className = 'format-detected';
    return detected;
}

// Format relative time difference in YY:MM:DD hh:mm:ss format
function formatRelativeTime(timestamp, now) {
    const diff = timestamp - now;
    const absDiff = Math.abs(diff);
    
    // Calculate total time difference components
    const totalSeconds = Math.floor(absDiff / 1000);
    const totalMinutes = Math.floor(totalSeconds / 60);
    const totalHours = Math.floor(totalMinutes / 60);
    const totalDays = Math.floor(totalHours / 24);
    
    // Calculate years and remaining days
    const years = Math.floor(totalDays / 365);
    const remainingDays = totalDays % 365;
    
    // Calculate months and remaining days
    const months = Math.floor(remainingDays / 30);
    const days = remainingDays % 30;
    
    // Calculate hours, minutes, seconds
    const hours = totalHours % 24;
    const minutes = totalMinutes % 60;
    const seconds = totalSeconds % 60;
    
    const lang = translations[currentLang];
    const prefix = diff > 0 ? lang.futurePrefix : lang.pastPrefix;
    
    // Format each component with leading zeros
    const yearsStr = String(years).padStart(2, '0');
    const monthsStr = String(months).padStart(2, '0');
    const daysStr = String(days).padStart(2, '0');
    const hoursStr = String(hours).padStart(2, '0');
    const minutesStr = String(minutes).padStart(2, '0');
    const secondsStr = String(seconds).padStart(2, '0');
    
    return `${prefix} ${yearsStr}:${monthsStr}:${daysStr} ${hoursStr}:${minutesStr}:${secondsStr}`;
}

// Global variables for real-time updates
let timestampUpdateInterval = null;
let dateUpdateInterval = null;
let currentTimestampValue = null;
let currentDateValue = null;

// Function to update timestamp relative time
function updateTimestampRelativeTime() {
    if (!currentTimestampValue) return;

    const now = new Date();
    const relativeString = formatRelativeTime(currentTimestampValue, now.getTime());
    document.getElementById('timestamp-relative').textContent = relativeString;
}

// Function to update date relative time
function updateDateRelativeTime() {
    if (!currentDateValue) return;

    const now = new Date();
    const relativeString = formatRelativeTime(currentDateValue, now.getTime());
    document.getElementById('date-relative').textContent = relativeString;
}

// Timestamp conversion function
function initTimestampInput() {
    const timestampInput = document.getElementById('timestamp-input');
    if (!timestampInput) {
        // Don't log error if element doesn't exist (normal for some pages)
        return;
    }

    timestampInput.addEventListener('input', function () {
        const timestamp = this.value.trim();
        if (!timestamp) {
            clearTimestampResults();
            document.getElementById('timestamp-format').textContent = '';
            document.getElementById('timestamp-format').className = '';

            // Clear interval and reset value
            if (timestampUpdateInterval) {
                clearInterval(timestampUpdateInterval);
                timestampUpdateInterval = null;
            }
            currentTimestampValue = null;
            return;
        }

        const detected = detectAndDisplayTimestampFormat(timestamp);
        if (!detected) {
            clearTimestampResults();

            // Clear interval and reset value
            if (timestampUpdateInterval) {
                clearInterval(timestampUpdateInterval);
                timestampUpdateInterval = null;
            }
            currentTimestampValue = null;
            return;
        }

        const {value} = detected;
        const date = new Date(value);
        const now = new Date();

        // Store current timestamp value for real-time updates
        currentTimestampValue = value;

        // Update GMT time
        const gmtString = date.toUTCString();
        document.getElementById('timestamp-gmt').textContent = gmtString;

        // Update local time
        const localString = date.toLocaleString();
        document.getElementById('timestamp-local').textContent = localString;

        // Update relative time initially
        const relativeString = formatRelativeTime(date.getTime(), now.getTime());
        document.getElementById('timestamp-relative').textContent = relativeString;

        // Start real-time updates for relative time
        if (timestampUpdateInterval) {
            clearInterval(timestampUpdateInterval);
        }
        timestampUpdateInterval = setInterval(updateTimestampRelativeTime, 1000);
    });
}

// Clear timestamp results
function clearTimestampResults() {
    document.getElementById('timestamp-gmt').textContent = '';
    document.getElementById('timestamp-local').textContent = '';
    document.getElementById('timestamp-relative').textContent = '';
}

// Date conversion function
function initDateInput() {
    const dateInput = document.getElementById('date-input');
    if (!dateInput) {
        // Don't log error if element doesn't exist (normal for some pages)
        return;
    }

    dateInput.addEventListener('input', function () {
        const dateString = this.value.trim();
        if (!dateString) {
            clearDateResults();

            // Clear interval and reset value
            if (dateUpdateInterval) {
                clearInterval(dateUpdateInterval);
                dateUpdateInterval = null;
            }
            currentDateValue = null;
            return;
        }

        const date = new Date(dateString);
        if (isNaN(date.getTime())) {
            clearDateResults();

            // Clear interval and reset value
            if (dateUpdateInterval) {
                clearInterval(dateUpdateInterval);
                dateUpdateInterval = null;
            }
            currentDateValue = null;
            return;
        }

        const now = new Date();

        // Store current date value for real-time updates
        currentDateValue = date.getTime();

        // Update timestamp (seconds)
        const timestampSeconds = Math.floor(date.getTime() / 1000);
        document.getElementById('date-timestamp-seconds').textContent = timestampSeconds;

        // Update timestamp (milliseconds)
        const timestampMilliseconds = date.getTime();
        document.getElementById('date-timestamp-milliseconds').textContent = timestampMilliseconds;

        // Update GMT time
        const gmtString = date.toUTCString();
        document.getElementById('date-gmt').textContent = gmtString;

        // Update local time
        const localString = date.toLocaleString();
        document.getElementById('date-local').textContent = localString;

        // Update relative time initially
        const relativeString = formatRelativeTime(date.getTime(), now.getTime());
        document.getElementById('date-relative').textContent = relativeString;

        // Start real-time updates for relative time
        if (dateUpdateInterval) {
            clearInterval(dateUpdateInterval);
        }
        dateUpdateInterval = setInterval(updateDateRelativeTime, 1000);
    });
}

    // Clear date results
function clearDateResults() {
    document.getElementById('date-timestamp-seconds').textContent = '';
    document.getElementById('date-timestamp-milliseconds').textContent = '';
    document.getElementById('date-gmt').textContent = '';
    document.getElementById('date-local').textContent = '';
    document.getElementById('date-relative').textContent = '';
}

// Add ripple effect on click
document.addEventListener('click', function(e) {
  // Skip ripple effect for links to avoid interfering with navigation
  if (e.target.tagName === 'A' || e.target.closest('a')) {
    return;
  }

  // Create ripple container if it doesn't exist
  let rippleContainer = document.querySelector('.ripple-container');
  if (!rippleContainer) {
    rippleContainer = document.createElement('div');
    rippleContainer.className = 'ripple-container';
    rippleContainer.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: 9999;
      overflow: hidden;
    `;
    document.body.appendChild(rippleContainer);
  }

  // Create ripple element
  const ripple = document.createElement('div');
  const size = 80; // Slightly larger fixed size
  const colors = ['#4ECDC4', '#45B7D1', '#96CEB4', '#88D8B0'];
  const color = colors[Math.floor(Math.random() * colors.length)];
  
  // Set ripple styles
  ripple.style.cssText = `
    position: absolute;
    width: ${size}px;
    height: ${size}px;
    background: radial-gradient(circle, ${color} 0%, ${color}40 100%);
    border-radius: 50%;
    transform: translate(-50%, -50%) scale(0);
    animation: ripple 1s ease-out;
    pointer-events: none;
    opacity: 0;
  `;
  
  // Position the ripple
  ripple.style.left = `${e.clientX}px`;
  ripple.style.top = `${e.clientY}px`;
  
  // Add ripple to container
  rippleContainer.appendChild(ripple);
  
  // Trigger animation
  requestAnimationFrame(() => {
    ripple.style.opacity = '1';
    ripple.style.transform = `translate(-50%, -50%) scale(1.5)`;
  });
  
  // Remove ripple after animation
  setTimeout(() => {
    ripple.style.opacity = '0';
    setTimeout(() => {
      ripple.remove();
      // Remove container if empty
      if (rippleContainer && rippleContainer.children.length === 0) {
        rippleContainer.remove();
      }
    }, 500);
  }, 1000);
});

// Add keyframe animation for ripple effect
const style = document.createElement('style');
style.textContent = `
  @keyframes ripple {
    to {
      opacity: 0;
      transform: translate(-50%, -50%) scale(1.8);
    }
  }
`;
document.head.appendChild(style);

// Add a subtle hover effect to clickable elements
const clickableElements = document.querySelectorAll('a, button, .header-btn, .language-btn, .copy-btn');
clickableElements.forEach(el => {
  el.style.transition = 'transform 0.2s ease';
  
  el.addEventListener('mouseover', () => {
    el.style.transform = 'scale(1.03)';
  });
  
  el.addEventListener('mouseout', () => {
    el.style.transform = 'scale(1)';
  });
});

// Initialize timestamp and date inputs with current time
function initializeInputs() {
    try {
        console.log('Starting initializeInputs...');
        const now = new Date();
        const currentTimestampSeconds = Math.floor(now.getTime() / 1000);

        // Initialize timestamp input
        const timestampInput = document.getElementById('timestamp-input');
        if (timestampInput) {
            console.log('Found timestamp input, setting value to:', currentTimestampSeconds);
            timestampInput.value = currentTimestampSeconds;

            // Force trigger input event with multiple methods to ensure it works
            setTimeout(() => {
                const inputEvent = new Event('input', { bubbles: true, cancelable: true });
                timestampInput.dispatchEvent(inputEvent);

                const changeEvent = new Event('change', { bubbles: true, cancelable: true });
                timestampInput.dispatchEvent(changeEvent);

                console.log('Timestamp input events dispatched');
            }, 50);
        } else {
            // Don't log error if element doesn't exist (normal for some pages)
        }

        // Initialize date input (datetime-local format: YYYY-MM-DDTHH:mm:ss)
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');
        const seconds = String(now.getSeconds()).padStart(2, '0');
        const formattedDate = `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;

        const dateInput = document.getElementById('date-input');
        if (dateInput) {
            console.log('Found date input, setting value to:', formattedDate);
            dateInput.value = formattedDate;

            // Force trigger input event with multiple methods to ensure it works
            setTimeout(() => {
                const inputEvent = new Event('input', { bubbles: true, cancelable: true });
                dateInput.dispatchEvent(inputEvent);

                const changeEvent = new Event('change', { bubbles: true, cancelable: true });
                dateInput.dispatchEvent(changeEvent);

                console.log('Date input events dispatched');
            }, 50);
        } else {
            // Don't log error if element doesn't exist (normal for some pages)
        }

        console.log('initializeInputs completed');
    } catch (error) {
        console.error('Error in initializeInputs:', error);
    }
}

// Toggle collapsible content
function toggleCollapsible(button) {
  try {
    if (!button) {
      console.error('No button provided to toggleCollapsible');
      return;
    }
    
    const expanded = button.getAttribute('aria-expanded') === 'true';
    const targetId = button.getAttribute('aria-controls');
    
    if (!targetId) {
      console.error('No aria-controls attribute found on button:', button);
      return;
    }
    
    const target = document.getElementById(targetId);
    
    if (!target) {
      console.error('Target element not found with ID:', targetId);
      return;
    }
    
    const newState = !expanded;
    
    // Update button state
    button.setAttribute('aria-expanded', String(newState));
    
    // Toggle the expanded class for icon rotation and styling
    if (newState) {
      button.classList.add('expanded');
      target.classList.add('visible');
      target.hidden = false;
    } else {
      button.classList.remove('expanded');
      // Wait for the transition to complete before hiding the element
      target.addEventListener('transitionend', function handler() {
        if (!button.classList.contains('expanded')) {
          target.hidden = true;
        }
        target.removeEventListener('transitionend', handler);
      }, { once: true });
      target.classList.remove('visible');
    }
  } catch (error) {
    console.error('Error in toggleCollapsible:', error);
  }
}

// Initialize collapsible buttons with retry mechanism
function initCollapsibles() {
  try {
    const collapsibleButtons = document.querySelectorAll('[aria-controls]');
    let initializedCount = 0;
    
    collapsibleButtons.forEach(button => {
      try {
        const targetId = button.getAttribute('aria-controls');
        if (!targetId) {
          console.error('Button missing aria-controls attribute:', button);
          return;
        }
        
        const target = document.getElementById(targetId);
        if (!target) {
          console.error(`Target element not found for ID: ${targetId}`, button);
          return;
        }
        
        // Set initial state
        const isExpanded = button.getAttribute('aria-expanded') === 'true';
        target.hidden = !isExpanded;
        
        // Add click event
        button.addEventListener('click', (e) => {
          e.preventDefault();
          toggleCollapsible(button);
        });
        
        // Add CSS class for styling
        button.classList.add('collapsible-button');
        target.classList.add('collapsible-content');
        
        initializedCount++;
      } catch (error) {
        console.error('Error initializing collapsible button:', error);
      }
    });
    
    console.log(`Initialized ${initializedCount} collapsible sections`);
    return initializedCount > 0;
  } catch (error) {
    console.error('Error in initCollapsibles:', error);
    return false;
  }
}

// Initialize the application
function initApp() {
  try {
    // Initialize UI with default language
    updateUIText();

    // Initialize event listeners first
    initTimestampInput();
    initDateInput();

    // Load custom minutes from cache
    loadCustomMinutesCache();

    // Initialize inputs with a small delay to ensure DOM is ready
    setTimeout(() => {
      initializeInputs();
    }, 100);

    // Try to initialize collapsibles
    const initialized = initCollapsibles();

    // If initialization failed, try again after a short delay
    if (!initialized) {
      console.log('Retrying collapsible initialization...');
      setTimeout(initCollapsibles, 300);
    }

    // Initialize and update current time every second (only on main page)
    if (isMainPage) {
      updateCurrentTime();
      setInterval(updateCurrentTime, 1000);
    }
  } catch (error) {
    console.error('Error in initApp:', error);
  }
}

// Wait for the document to be fully loaded
function domReady() {
  try {
    if (document.readyState === 'complete' || document.readyState === 'interactive') {
      // Document is already ready, initialize immediately
      setTimeout(initApp, 1);
    } else {
      // Wait for DOMContentLoaded
      document.addEventListener('DOMContentLoaded', initApp);
    }
  } catch (error) {
    console.error('Error in domReady:', error);
  }
}

// Start the initialization process
domReady();

// Email copy functionality
document.addEventListener('click', function(e) {
    // Check if the click was on a copy email button
    const copyBtn = e.target.closest('.copy-email-btn');
    if (copyBtn) {
        e.preventDefault();
        e.stopPropagation();
        
        // Prevent default and stop propagation to avoid any parent handlers
        if (e.cancelable) e.preventDefault();
        e.stopImmediatePropagation();
        
        const email = copyBtn.getAttribute('data-email');
        if (email) {
            // Store the original content
            const originalHTML = copyBtn.innerHTML;

            // Show green checkmark with animation
            copyBtn.innerHTML = '✓';
            copyBtn.style.color = '#4CAF50';
            copyBtn.style.backgroundColor = 'rgba(76, 175, 80, 0.1)';
            copyBtn.style.borderRadius = '50%';
            copyBtn.style.transition = 'all 0.2s ease';
            copyBtn.style.animation = 'checkmarkBounce 0.4s ease-out';

            // Add success animation class
            copyBtn.classList.add('copied');
            
            // Create a temporary input element that's not visible
            const tempInput = document.createElement('input');
            // Position the input off-screen
            tempInput.style.position = 'fixed';
            tempInput.style.opacity = '0';
            tempInput.style.pointerEvents = 'none';
            tempInput.style.left = '-1000px';
            tempInput.value = email;
            
            // Add to body and focus without scrolling
            document.body.appendChild(tempInput);
            
            try {
                // Select the text without focusing (which would cause scrolling)
                tempInput.focus({ preventScroll: true });
                tempInput.select();
                tempInput.setSelectionRange(0, 99999); // For mobile devices
                
                // Copy the text
                const successful = document.execCommand('copy');
                
                if (!successful) {
                    // Fallback to prompt if copy fails
                    prompt('Copy to clipboard: Ctrl+C, Enter', email);
                }
            } catch (err) {
                console.error('Failed to copy email: ', err);
                // Fallback to prompt if execCommand fails
                prompt('Copy to clipboard: Ctrl+C, Enter', email);
            } finally {
                // Clean up and restore focus to where it was
                const activeElement = document.activeElement;
                document.body.removeChild(tempInput);
                if (activeElement) {
                    activeElement.focus({ preventScroll: true });
                }
                
                // Restore original appearance after delay
                setTimeout(() => {
                    copyBtn.innerHTML = originalHTML;
                    copyBtn.style.color = '';
                    copyBtn.style.backgroundColor = '';
                    copyBtn.style.borderRadius = '';
                    copyBtn.style.transform = '';
                    copyBtn.style.transition = '';
                    copyBtn.style.animation = '';
                    copyBtn.classList.remove('copied');
                }, 1500);
            }
        }
    }
});

// Clean up intervals when page is unloaded
window.addEventListener('beforeunload', function () {
    if (timestampUpdateInterval) {
        clearInterval(timestampUpdateInterval);
        timestampUpdateInterval = null;
    }
    if (dateUpdateInterval) {
        clearInterval(dateUpdateInterval);
        dateUpdateInterval = null;
    }
});
