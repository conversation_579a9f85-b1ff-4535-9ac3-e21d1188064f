<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>更多工具 - Unix Timestamp Converter</title>
  <link rel="icon" href="/icon.png" type="image/png">
  <link rel="stylesheet" href="/style.css">
  <style>
    .tools-container {
      margin-top: 20px;
    }
    .tools-list {
      list-style: none;
      padding: 0;
    }
    .tools-list li {
      margin-bottom: 15px;
      padding: 15px;
      background-color: #f8f9fa;
      border-radius: 5px;
      transition: all 0.3s ease;
    }
    .tools-list li:hover {
      background-color: #e9ecef;
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    .tool-link {
      display: block;
      color: #333;
      text-decoration: none;
      font-weight: bold;
    }
    .tool-description {
      margin-top: 5px;
      color: #666;
      font-size: 14px;
    }
    .back-link {
      display: inline-block;
      margin-top: 20px;
      color: #007bff;
      text-decoration: none;
    }
    .back-link:hover {
      text-decoration: underline;
    }
  </style>
</head>
<body>
<div class="container">
  <div class="header">
    <h1>更多工具</h1>
    <div class="header-controls">
      <a href="/" class="tools-btn">返回主页</a>
    </div>
  </div>

  <div class="tools-container">
    <ul class="tools-list">
      <li>
        <a href="/case-converter.html" class="tool-link">下划线转驼峰工具</a>
        <p class="tool-description">将下划线命名法(snake_case)转换为驼峰命名法(CamelCase)，或者反向转换。适用于编程变量命名转换。</p>
      </li>
      <!-- 未来可以在这里添加更多工具 -->
    </ul>
  </div>

  <div class="footer">
    <span>© 2025 Unix Timestamp Converter - 实用工具集合</span>
  </div>
</div>
</body>
</html>