* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
}

body {
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  margin: 0;
  padding: 0;
}

.container {
  width: 100%;
  max-width: 800px;
  background-color: white;
  border-radius: 5px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin: 20px auto;
  flex: 1 1 auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.header h1 {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin: 0;
}

.header-left {
  flex: 1;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.header-btn {
  display: inline-flex;
  align-items: center;
  padding: 8px 16px;
  background-color: #f8f9fa;
  color: #333;
  text-decoration: none;
  border-radius: 6px;
  border: 1px solid #dee2e6;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.header-btn:hover {
  background-color: #e9ecef;
  border-color: #adb5bd;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.features-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: #667eea;
}

.features-btn:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  color: white;
  border-color: #5a6fd8;
}

.case-converter-btn {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  border-color: #f093fb;
}

.case-converter-btn:hover {
  background: linear-gradient(135deg, #ee82f0 0%, #f3455a 100%);
  color: white;
  border-color: #ee82f0;
}

/* Tools Selector */
.tools-selector {
  position: relative;
  display: inline-block;
}

.tools-btn {
  background-color: #f8f9fa;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 5px 10px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  color: #333;
  font-weight: 500;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.tools-btn:after {
  content: "▼";
  font-size: 10px;
  margin-left: 5px;
}

.tools-btn:hover {
  background-color: #e9ecef;
  border-color: #adb5bd;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.tools-dropdown {
  display: none;
  position: absolute;
  right: 0;
  top: 100%;
  background-color: white;
  min-width: 180px;
  box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
  z-index: 1000;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.tools-dropdown.show {
  display: block;
}

.tools-option {
  display: block;
  padding: 10px 15px;
  color: #333;
  text-decoration: none;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
  font-size: 14px;
}

.tools-option:last-child {
  border-bottom: none;
}

.tools-option:hover {
  background-color: #f8f9fa;
  color: #333;
}

.language-selector {
  position: relative;
  display: inline-block;
}

.language-btn {
  background-color: #f8f9fa;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 5px 10px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.language-btn:after {
  content: "▼";
  font-size: 10px;
  margin-left: 5px;
}

.language-dropdown {
  display: none;
  position: absolute;
  right: 0;
  top: 100%;
  background-color: white;
  min-width: 160px;
  box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
  z-index: 1;
  border-radius: 4px;
  max-height: 300px;
  overflow-y: auto;
}

.language-dropdown.show {
  display: block;
}

.language-option {
  padding: 10px 15px;
  text-decoration: none;
  display: block;
  color: #333;
  font-size: 14px;
  cursor: pointer;
}

.language-option:hover {
  background-color: #f1f1f1;
}

.current-time {
  margin-bottom: 30px;
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 5px;
}

.time-row {
  display: flex;
  margin-bottom: 10px;
  align-items: center;
}

.time-row:last-child {
  margin-bottom: 0;
}

.time-label {
  width: 150px;
  font-size: 14px;
  color: #555;
}

.time-value {
  flex: 1;
  font-size: 14px;
  color: #333;
}

.mobile-hint {
  display: none;
  background-color: #e3f2fd;
  border: 1px solid #2196f3;
  border-radius: 6px;
  padding: 8px 12px;
  margin-bottom: 15px;
  font-size: 14px;
  color: #1976d2;
  text-align: center;
}

/* Show PC hint only on PC (non-touch devices) */
@media (min-width: 769px) and (pointer: fine) {
  #pc-hint {
    display: block;
  }
  #mobile-hint {
    display: none;
  }
}

/* Highlight row style - modern */
table tr {
  transition: all 0.2s ease;
  position: relative;
  border-radius: 4px;
  overflow: hidden;
}

table tr:not(.highlight-row):hover {
  background-color: #f1f8ff;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 120, 255, 0.15);
  transform: translateY(-1px);
}

.highlight-row {
  font-weight: 600;
  background-color: #e6f2ff;
  border-left: 4px solid #1976d2;
  color: #0d47a1;
  transition: all 0.2s ease;
  position: relative;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.highlight-row:hover {
  background-color: #d0e3ff;
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.2);
  transform: translateY(-1px);
}

table tr.highlight-row:hover {
  background-color: #d0e3ff;
}

table tr.highlight-row td {
  padding: 12px 15px;
  font-weight: 600;
  color: #0d47a1;
}

table tr:not(.highlight-row) td {
  padding: 10px 15px;
  transition: all 0.2s ease;
}

table tr:not(.highlight-row):hover td {
  color: #0056b3;
}

table tr.highlight-row td {
  padding: 10px 12px;
  color: #0d47a1;
}

/* Show mobile hint only on mobile (touch devices) */
@media (max-width: 768px) and (pointer: coarse) {
  #pc-hint {
    display: none;
  }
  #mobile-hint {
    display: block;
  }
}

.copy-btn-container {
  position: relative;
  display: inline-block;
  margin-left: 10px;
}

.copy-btn {
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  cursor: pointer;
  font-size: 16px;
  min-width: 40px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.copy-btn:hover {
  background-color: #0056b3;
}

.copy-btn:active {
  background-color: #004085;
  transform: scale(0.98);
}

/* Mobile-specific styles */
@media (max-width: 768px) {
  .copy-btn {
    padding: 8px 12px;
    font-size: 16px;
    min-height: 44px; /* iOS recommended touch target size */
    touch-action: manipulation; /* Disable double-tap zoom */
  }

  .copy-btn-container {
    margin-left: 8px;
  }

  .copy-dropdown {
    min-width: 160px;
    right: -10px; /* Adjust for mobile screens */
  }

  .copy-option {
    padding: 12px 16px;
    font-size: 16px;
    min-height: 44px;
    display: flex;
    align-items: center;
  }

  .custom-option {
    padding: 10px 16px;
  }

  .custom-minutes-input {
    width: 70px;
    padding: 4px 6px;
    font-size: 16px;
    min-height: 32px;
  }

  .custom-copy-btn {
    padding: 4px 8px;
    font-size: 14px;
    min-height: 32px;
  }
}

.copy-dropdown {
  display: none;
  position: absolute;
  right: 0;
  top: 100%;
  background-color: white;
  min-width: 140px;
  box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
  z-index: 1000;
  border-radius: 4px;
  border: 1px solid #ddd;
  margin-top: 2px;
}

.copy-dropdown.show {
  display: block;
}

.copy-option {
  padding: 8px 12px;
  cursor: pointer;
  font-size: 14px;
  color: #333;
  border-bottom: 1px solid #eee;
}

.copy-option:last-child {
  border-bottom: none;
}

.copy-option:hover {
  background-color: #f8f9fa;
}

.custom-option {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 6px 12px;
}

.custom-minutes-input {
  width: 60px;
  padding: 2px 4px;
  border: 1px solid #ddd;
  border-radius: 3px;
  font-size: 12px;
  text-align: center;
}

.custom-minutes-input:focus {
  outline: none;
  border-color: #007bff;
}

.custom-option span {
  font-size: 12px;
  color: #666;
  flex: 1;
}

.custom-copy-btn {
  background-color: #28a745;
  color: white;
  border: none;
  border-radius: 3px;
  padding: 2px 6px;
  font-size: 11px;
  cursor: pointer;
  margin-left: 5px;
}

.custom-copy-btn:hover {
  background-color: #218838;
}

.custom-copy-btn:active {
  background-color: #1e7e34;
}

.section {
  margin-bottom: 30px;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #333;
}

.section-desc {
  font-size: 14px;
  color: #666;
  margin-bottom: 15px;
}

input[type="text"] {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  margin-bottom: 15px;
}

/* Date picker styles */
.date-input-container {
  margin-bottom: 15px;
}

.date-picker {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  background-color: #fff;
  color: #333;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  box-sizing: border-box;
}

.date-picker:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.date-picker:hover {
  border-color: #adb5bd;
}

.date-input-hint {
  font-size: 12px;
  color: #666;
  margin-top: 5px;
  text-align: center;
  font-style: italic;
}

/* Mobile optimization for date picker */
@media (max-width: 768px) {
  .date-picker {
    padding: 12px;
    font-size: 16px; /* Prevent zoom on iOS */
    min-height: 44px; /* iOS recommended touch target size */
  }

  .date-input-hint {
    font-size: 13px;
  }
}

table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 15px;
}

table td {
  border: 1px solid #ddd;
  padding: 8px 12px;
  font-size: 14px;
  color: #333;
}

table td:first-child {
  width: 150px;
  background-color: #f8f9fa;
}

.format-detected {
  color: #28a745;
  font-weight: bold;
}

.format-error {
  color: #dc3545;
  font-weight: bold;
}

/* Collapsible Section */
.about-section {
  margin: 30px 0;
  padding: 15px;
  background: #f8f8f8;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.about-title {
  margin: 0;
  padding: 0;
}

.toggle-button {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 10px 15px;
  background: transparent;
  border: none;
  border-radius: 4px;
  color: #333;
  font-size: 1.5em;
  font-weight: 600;
  text-align: left;
  cursor: pointer;
  transition: all 0.2s ease;
}

.toggle-button:hover {
  background: rgba(0, 0, 0, 0.05);
}

.toggle-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.toggle-icon {
  transition: transform 0.3s ease;
  margin-left: 10px;
  flex-shrink: 0;
}

.toggle-button.expanded .toggle-icon {
  transform: rotate(180deg);
}

.about-content {
  overflow: hidden;
  transition: max-height 0.3s ease, opacity 0.2s ease;
  max-height: 0;
  opacity: 0;
}

.about-content.visible {
  max-height: 5000px; /* Adjust based on your content */
  opacity: 1;
  padding-top: 15px;
}

.about-content h3 {
  margin: 20px 0 10px 0;
  color: #444;
  font-size: 1.2em;
}

.about-content p, .about-content ul {
  margin: 10px 0;
  line-height: 1.6;
  color: #555;
}

.about-content ul {
  padding-left: 20px;
}

.about-content li {
  margin-bottom: 5px;
}

.about-content li {
  font-size: 14px;
  color: #555;
  line-height: 1.5;
}

.footer {
  width: 100%;
  background-color: #f8f9fa;
  padding: 25px 0;
  margin-top: 40px;
  border-top: 1px solid #e0e0e0;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

.footer-content {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 20px;
  text-align: center;
}

.footer p {
  color: #555;
  margin: 0 0 15px 0;
  font-size: 14px;
  line-height: 1.5;
}

.contact-email {
  margin-top: 15px;
  position: relative;
  display: inline-block;
  padding-right: 30px; /* Make room for the copy button */
}

.contact-link {
  display: inline-flex;
  align-items: center;
  background-color: #1976d2;
  color: white !important;
  text-decoration: none;
  padding: 10px 15px 10px 12px;  /* Reduced right padding, reduced left padding */
  border-radius: 25px;
  transition: all 0.3s ease;
  font-weight: 500;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  position: relative;
  padding-right: 45px;
}

.contact-link:hover {
  background-color: #1565c0;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  text-decoration: none;
}

.contact-link:active {
  transform: translateY(0);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.contact-icon {
  margin-right: 8px;
  font-size: 16px;
}

.copy-email-btn {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  padding: 2px 2px;  /* Increased padding */
  margin: 0 0 0 4px;  /* Adjusted margin */
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  position: relative;
  top: 0;
  outline: none;
  vertical-align: middle;
  transform: scale(1);  /* Reset to full size */
  transform-origin: center center;
}

.copy-email-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.copy-email-btn:active {
  transform: translateY(-50%) scale(0.95);
}

.copy-email-btn svg {
  width: 16px;
  height: 16px;
  transition: all 0.3s ease;
}

@keyframes copiedPulse {
  0% { 
    transform: scale(1); 
    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7); 
  }
  70% { 
    transform: scale(1.2); 
    box-shadow: 0 0 0 8px rgba(76, 175, 80, 0); 
  }
  100% { 
    transform: scale(1); 
    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0); 
  }
}

.copy-email-btn.copied {
  animation: copiedPulse 0.8s ease-out;
  color: #4CAF50 !important;
  background-color: rgba(76, 175, 80, 0.1) !important;
  border-radius: 50% !important;
  transform: scale(1.1) !important;
}

.copy-email-btn.copied svg {
  fill: #4CAF50;
  transform: scale(1.2);
}

/* Enhanced checkmark animation */
@keyframes checkmarkBounce {
  0% {
    transform: scale(0.8);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(1.1);
    opacity: 1;
  }
}



/* Responsive adjustments */
@media (max-width: 600px) {
  .footer {
    padding: 20px 0;
  }
  
  .footer p {
    font-size: 13px;
    margin-bottom: 12px;
  }
  
  .contact-link {
    padding: 8px 16px;
    font-size: 14px;
  }
}

/* Mobile responsiveness for header */
@media (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .header-left {
    text-align: center;
  }

  .header-right {
    justify-content: center;
    gap: 0.5rem;
  }

  .header-btn {
    padding: 6px 12px;
    font-size: 13px;
  }

  .header h1 {
    font-size: 20px;
  }
}

@media (max-width: 480px) {
  .header-right {
    flex-direction: column;
    gap: 0.5rem;
  }

  .header-btn {
    width: 100%;
    justify-content: center;
  }

  .tools-selector {
    width: 100%;
  }

  .tools-btn {
    width: 100%;
    justify-content: center;
  }

  .language-selector {
    width: 100%;
  }

  .language-btn {
    width: 100%;
    justify-content: center;
  }
}
