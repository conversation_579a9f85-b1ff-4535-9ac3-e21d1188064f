// Features page JavaScript
// Import translations from main script
let currentLang = 'en-US';

// Features page translations
const featuresTranslations = {
    'en-US': {
        // Navigation
        navHome: '🏠 Home',
        navCaseConverter: '🔤 Case Converter',
        
        // Hero section
        featuresTitle: '🌟 Powerful Features',
        featuresSubtitle: 'Discover what makes our Unix Timestamp Converter the best tool for developers',
        
        // Feature cards
        copySystemTitle: 'Smart Copy System',
        copySystemDesc: 'Advanced copying functionality with multiple interaction methods',
        copyFeature1: '📋 Icon-based copy buttons for clean interface',
        copyFeature2: '🖱️ Double-click for quick copy menu',
        copyFeature3: '✅ Green checkmark success animation',
        copyFeature4: '🚫 Prevents duplicate triggers during double-click',
        
        quickCopyTitle: 'Quick Time Copy',
        quickCopyDesc: 'Instantly copy future timestamps with preset intervals',
        quickFeature1: '⏱️ 1, 3, 5 minute preset intervals',
        quickFeature2: '🎯 Custom minute input with caching',
        quickFeature3: '🔄 Real-time calculation of future timestamps',
        quickFeature4: '💾 Remembers your custom preferences',
        
        multilangTitle: 'Multi-language Support',
        multilangDesc: 'Complete localization for global accessibility',
        multilangFeature1: '🗣️ 10 languages supported',
        multilangFeature2: '🔗 Unique URL paths for each language',
        multilangFeature3: '💡 Smart device-specific hints',
        multilangFeature4: '🔍 SEO optimized for each language',
        
        mobileTitle: 'Mobile Optimization',
        mobileDesc: 'Perfect mobile experience with touch-friendly design',
        mobileFeature1: '👆 Double-tap support on mobile',
        mobileFeature2: '📐 Responsive design for all screen sizes',
        mobileFeature3: '📳 Haptic feedback support',
        mobileFeature4: '🔧 Special optimization for Huawei browsers',
        
        detectionTitle: 'Smart Format Detection',
        detectionDesc: 'Automatically recognizes different timestamp formats',
        detectionFeature1: '⏰ Seconds (10 digits)',
        detectionFeature2: '⚡ Milliseconds (13 digits)',
        detectionFeature3: '🔬 Microseconds (16 digits)',
        detectionFeature4: '⚛️ Nanoseconds (19 digits)',
        
        fallbackTitle: 'Advanced Copy Fallback',
        fallbackDesc: 'Multiple copy methods ensure compatibility across all devices',
        fallbackFeature1: '🔒 Modern Clipboard API (HTTPS)',
        fallbackFeature2: '📋 Legacy execCommand method',
        fallbackFeature3: '📝 Manual copy dialog fallback',
        fallbackFeature4: '✅ Works on all browsers and devices',
        
        // Technical section
        techTitle: '🔧 Technical Highlights',
        techPerformance: '⚡ Performance',
        techPerformanceDesc: 'Lightweight, fast loading, no external dependencies',
        techCompatibility: '🌐 Compatibility',
        techCompatibilityDesc: 'Works on all modern browsers and mobile devices',
        techSecurity: '🔒 Security',
        techSecurityDesc: 'HTTPS encryption, secure clipboard operations',
        techAccessibility: '♿ Accessibility',
        techAccessibilityDesc: 'Keyboard navigation, screen reader support',
        
        // CTA section
        ctaTitle: 'Ready to try it out?',
        ctaSubtitle: 'Experience the most powerful Unix timestamp converter',
        ctaButton: '🚀 Start Converting',
        
        // Footer
        featuresFooterText: '© 2025 Unix Timestamp Converter - The ultimate tool for developers worldwide'
    },
    'zh-CN': {
        // Navigation
        navHome: '🏠 首页',
        navCaseConverter: '🔤 大小写转换',
        
        // Hero section
        featuresTitle: '🌟 强大功能',
        featuresSubtitle: '探索让我们的Unix时间戳转换器成为开发者最佳工具的特性',
        
        // Feature cards
        copySystemTitle: '智能复制系统',
        copySystemDesc: '具有多种交互方式的高级复制功能',
        copyFeature1: '📋 基于图标的复制按钮，界面简洁',
        copyFeature2: '🖱️ 双击显示快捷复制菜单',
        copyFeature3: '✅ 绿色对勾成功动画',
        copyFeature4: '🚫 防止双击时重复触发',
        
        quickCopyTitle: '快速时间复制',
        quickCopyDesc: '使用预设间隔即时复制未来时间戳',
        quickFeature1: '⏱️ 1、3、5分钟预设间隔',
        quickFeature2: '🎯 自定义分钟输入并缓存',
        quickFeature3: '🔄 实时计算未来时间戳',
        quickFeature4: '💾 记住您的自定义偏好',
        
        multilangTitle: '多语言支持',
        multilangDesc: '完整本地化，全球可访问',
        multilangFeature1: '🗣️ 支持10种语言',
        multilangFeature2: '🔗 每种语言独特的URL路径',
        multilangFeature3: '💡 智能设备特定提示',
        multilangFeature4: '🔍 每种语言SEO优化',
        
        mobileTitle: '移动端优化',
        mobileDesc: '触摸友好设计的完美移动体验',
        mobileFeature1: '👆 移动端双击支持',
        mobileFeature2: '📐 适配所有屏幕尺寸的响应式设计',
        mobileFeature3: '📳 触觉反馈支持',
        mobileFeature4: '🔧 华为浏览器特别优化',
        
        detectionTitle: '智能格式检测',
        detectionDesc: '自动识别不同的时间戳格式',
        detectionFeature1: '⏰ 秒（10位数字）',
        detectionFeature2: '⚡ 毫秒（13位数字）',
        detectionFeature3: '🔬 微秒（16位数字）',
        detectionFeature4: '⚛️ 纳秒（19位数字）',
        
        fallbackTitle: '高级复制降级',
        fallbackDesc: '多种复制方法确保所有设备兼容性',
        fallbackFeature1: '🔒 现代剪贴板API（HTTPS）',
        fallbackFeature2: '📋 传统execCommand方法',
        fallbackFeature3: '📝 手动复制对话框降级',
        fallbackFeature4: '✅ 适用于所有浏览器和设备',
        
        // Technical section
        techTitle: '🔧 技术亮点',
        techPerformance: '⚡ 性能',
        techPerformanceDesc: '轻量级，快速加载，无外部依赖',
        techCompatibility: '🌐 兼容性',
        techCompatibilityDesc: '适用于所有现代浏览器和移动设备',
        techSecurity: '🔒 安全性',
        techSecurityDesc: 'HTTPS加密，安全剪贴板操作',
        techAccessibility: '♿ 可访问性',
        techAccessibilityDesc: '键盘导航，屏幕阅读器支持',
        
        // CTA section
        ctaTitle: '准备试用了吗？',
        ctaSubtitle: '体验最强大的Unix时间戳转换器',
        ctaButton: '🚀 开始转换',
        
        // Footer
        featuresFooterText: '© 2025 Unix时间戳转换器 - 全球开发者的终极工具'
    }
    // Add more languages as needed...
};

// Language detection and initialization
function initializeLanguage() {
    // Check URL path for language
    const pathLang = window.location.pathname.replace(/^\//, '').replace(/\/$/, '').split('/')[0];
    const supportedLangs = ['zh-CN', 'hi-IN', 'ja-JP', 'de-DE', 'en-GB', 'ru-RU', 'ko-KR', 'en-CA', 'fr-FR'];
    
    if (supportedLangs.includes(pathLang)) {
        currentLang = pathLang;
    } else {
        // Check localStorage
        const savedLang = localStorage.getItem('lang');
        if (savedLang) {
            currentLang = savedLang;
        }
    }
    
    updateFeaturesUI();
}

// Update UI text based on current language
function updateFeaturesUI() {
    const lang = featuresTranslations[currentLang] || featuresTranslations['en-US'];
    
    // Update all text elements
    const elements = {
        'nav-home': lang.navHome,
        'nav-case-converter': lang.navCaseConverter,
        'features-title': lang.featuresTitle,
        'features-subtitle': lang.featuresSubtitle,
        'copy-system-title': lang.copySystemTitle,
        'copy-system-desc': lang.copySystemDesc,
        'copy-feature-1': lang.copyFeature1,
        'copy-feature-2': lang.copyFeature2,
        'copy-feature-3': lang.copyFeature3,
        'copy-feature-4': lang.copyFeature4,
        'quick-copy-title': lang.quickCopyTitle,
        'quick-copy-desc': lang.quickCopyDesc,
        'quick-feature-1': lang.quickFeature1,
        'quick-feature-2': lang.quickFeature2,
        'quick-feature-3': lang.quickFeature3,
        'quick-feature-4': lang.quickFeature4,
        'multilang-title': lang.multilangTitle,
        'multilang-desc': lang.multilangDesc,
        'multilang-feature-1': lang.multilangFeature1,
        'multilang-feature-2': lang.multilangFeature2,
        'multilang-feature-3': lang.multilangFeature3,
        'multilang-feature-4': lang.multilangFeature4,
        'mobile-title': lang.mobileTitle,
        'mobile-desc': lang.mobileDesc,
        'mobile-feature-1': lang.mobileFeature1,
        'mobile-feature-2': lang.mobileFeature2,
        'mobile-feature-3': lang.mobileFeature3,
        'mobile-feature-4': lang.mobileFeature4,
        'detection-title': lang.detectionTitle,
        'detection-desc': lang.detectionDesc,
        'detection-feature-1': lang.detectionFeature1,
        'detection-feature-2': lang.detectionFeature2,
        'detection-feature-3': lang.detectionFeature3,
        'detection-feature-4': lang.detectionFeature4,
        'fallback-title': lang.fallbackTitle,
        'fallback-desc': lang.fallbackDesc,
        'fallback-feature-1': lang.fallbackFeature1,
        'fallback-feature-2': lang.fallbackFeature2,
        'fallback-feature-3': lang.fallbackFeature3,
        'fallback-feature-4': lang.fallbackFeature4,
        'tech-title': lang.techTitle,
        'tech-performance': lang.techPerformance,
        'tech-performance-desc': lang.techPerformanceDesc,
        'tech-compatibility': lang.techCompatibility,
        'tech-compatibility-desc': lang.techCompatibilityDesc,
        'tech-security': lang.techSecurity,
        'tech-security-desc': lang.techSecurityDesc,
        'tech-accessibility': lang.techAccessibility,
        'tech-accessibility-desc': lang.techAccessibilityDesc,
        'cta-title': lang.ctaTitle,
        'cta-subtitle': lang.ctaSubtitle,
        'cta-button': lang.ctaButton,
        'features-footer-text': lang.featuresFooterText
    };
    
    // Apply translations
    Object.keys(elements).forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = elements[id];
        }
    });
    
    // Update language button
    const languageBtn = document.getElementById('language-btn');
    if (languageBtn) {
        const langNames = {
            'en-US': '🌐 English',
            'zh-CN': '🌐 中文',
            'hi-IN': '🌐 हिंदी',
            'ja-JP': '🌐 日本語',
            'de-DE': '🌐 Deutsch',
            'en-GB': '🌐 English (UK)',
            'ru-RU': '🌐 Русский',
            'ko-KR': '🌐 한국어',
            'en-CA': '🌐 English (CA)',
            'fr-FR': '🌐 Français'
        };
        languageBtn.textContent = langNames[currentLang] || '🌐 English';
    }
}

// Language selector functionality
document.addEventListener('DOMContentLoaded', function() {
    initializeLanguage();
    
    // Language dropdown toggle
    const languageBtn = document.getElementById('language-btn');
    const languageDropdown = document.getElementById('language-dropdown');
    
    if (languageBtn && languageDropdown) {
        languageBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            languageDropdown.classList.toggle('show');
        });
        
        // Language selection
        document.querySelectorAll('.language-option').forEach(option => {
            option.addEventListener('click', function() {
                const lang = this.getAttribute('data-lang');
                localStorage.setItem('lang', lang);
                currentLang = lang;
                updateFeaturesUI();
                languageDropdown.classList.remove('show');
            });
        });
        
        // Close dropdown when clicking outside
        document.addEventListener('click', function() {
            languageDropdown.classList.remove('show');
        });
    }
});
