<!DOCTYPE html>
<html lang="en">
<head>
  <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-LGJZZVSHEP"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', 'G-LGJZZVSHEP');
  </script>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Unix Timestamp Converter - Free Online Tool for Developers</title>
  <meta name="description" content="Free online Unix timestamp converter. Convert between Unix timestamps and human-readable dates. Supports seconds, milliseconds, microseconds, and nanoseconds.">
  <meta name="keywords" content="Unix timestamp, timestamp converter, epoch converter, date converter, developer tools">

  <link rel="canonical" href="https://unixtime.help">
  <!-- 多语言 hreflang 标签 -->
  <link rel="alternate" hreflang="en" href="https://unixtime.help/" />
  <link rel="alternate" hreflang="zh-CN" href="https://unixtime.help/zh-CN/" />
  <link rel="alternate" hreflang="hi-IN" href="https://unixtime.help/hi-IN/" />
  <link rel="alternate" hreflang="ja-JP" href="https://unixtime.help/ja-JP/" />
  <link rel="alternate" hreflang="de-DE" href="https://unixtime.help/de-DE/" />
  <link rel="alternate" hreflang="en-GB" href="https://unixtime.help/en-GB/" />
  <link rel="alternate" hreflang="ru-RU" href="https://unixtime.help/ru-RU/" />
  <link rel="alternate" hreflang="ko-KR" href="https://unixtime.help/ko-KR/" />
  <link rel="alternate" hreflang="en-CA" href="https://unixtime.help/en-CA/" />
  <link rel="alternate" hreflang="fr-FR" href="https://unixtime.help/fr-FR/" />
  <meta property="og:title" content="Unix Timestamp Converter - Free Online Tool">
  <meta property="og:description" content="Convert between Unix timestamps and human-readable dates. Ideal for developers and system administrators.">
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://unixtime.help">

  <link rel="icon" href="/icon.png" type="image/png">

  <link rel="stylesheet" href="/style.css">
</head>
<body>
<div class="container">
  <div class="header">
    <div class="header-left">
      <h1 id="page-title"><a href="/" style="text-decoration: none; color: inherit;">Unix Timestamp Converter</a></h1>
    </div>

    <div class="header-right">
      <!-- Tools Selector -->
      <div class="tools-selector">
        <button class="tools-btn" id="tools-btn">🔧 Other Tools</button>
        <div class="tools-dropdown" id="tools-dropdown">
          <a href="/features.html" class="tools-option" id="tools-features">✨ Features</a>
          <a href="/case-converter.html" class="tools-option" id="tools-case-converter">🔤 Case Converter</a>
        </div>
      </div>

      <!-- Donation button -->
      <a href="/donate.html" class="header-btn donate-btn" id="donate-btn">❤️ Donate</a>

      <!-- Language Selector -->
      <div class="language-selector">
        <button class="language-btn" id="language-btn">English (US)</button>
        <div class="language-dropdown" id="language-dropdown">
          <div class="language-option" data-lang="en-US">English (US)</div>
          <div class="language-option" data-lang="zh-CN">中文 (中国)</div>
          <div class="language-option" data-lang="hi-IN">हिन्दी (भारत)</div>
          <div class="language-option" data-lang="ja-JP">日本語 (日本)</div>
          <div class="language-option" data-lang="de-DE">Deutsch (Deutschland)</div>
          <div class="language-option" data-lang="en-GB">English (UK)</div>
          <div class="language-option" data-lang="ru-RU">Русский (Россия)</div>
          <div class="language-option" data-lang="ko-KR">한국어 (대한민국)</div>
          <div class="language-option" data-lang="en-CA">English (Canada)</div>
          <div class="language-option" data-lang="fr-FR">Français (France)</div>
        </div>
      </div>
    </div>
  </div>

  <div class="current-time">
    <div class="mobile-hint" id="pc-hint">
      <span id="pc-hint-text">💡 Tip: Double-click copy buttons for quick copy</span>
    </div>
    <div class="mobile-hint" id="mobile-hint">
      <span id="mobile-hint-text">💡 Tip: Long press copy buttons for quick copy</span>
    </div>
    <div class="time-row">
      <div class="time-label" id="current-seconds-label">Current Timestamp (s)</div>
      <div class="time-value" id="current-timestamp-seconds"></div>
      <div class="copy-btn-container">
        <button class="copy-btn" data-copy="current-timestamp-seconds" data-type="seconds" id="copy-seconds-btn">📋</button>
        <div class="copy-dropdown" id="copy-dropdown-seconds">
          <div class="copy-option" data-minutes="1" data-type="seconds" id="one-min-later-seconds">1 min later</div>
          <div class="copy-option" data-minutes="3" data-type="seconds" id="three-min-later-seconds">3 min later</div>
          <div class="copy-option" data-minutes="5" data-type="seconds" id="five-min-later-seconds">5 min later</div>
          <div class="copy-option custom-option" data-type="seconds">
            <input type="number" class="custom-minutes-input" id="custom-seconds-input" placeholder="Custom" min="1" max="9999">
            <span id="custom-min-later-seconds">min later</span>
            <button type="button" class="custom-copy-btn" data-type="seconds" id="custom-copy-btn-seconds">Copy</button>
          </div>
        </div>
      </div>
    </div>
    <div class="time-row">
      <div class="time-label" id="current-milliseconds-label">Current Timestamp (ms)</div>
      <div class="time-value" id="current-timestamp-milliseconds"></div>
      <div class="copy-btn-container">
        <button class="copy-btn" data-copy="current-timestamp-milliseconds" data-type="milliseconds" id="copy-milliseconds-btn">📋</button>
        <div class="copy-dropdown" id="copy-dropdown-milliseconds">
          <div class="copy-option" data-minutes="1" data-type="milliseconds" id="one-min-later-milliseconds">1 min later</div>
          <div class="copy-option" data-minutes="3" data-type="milliseconds" id="three-min-later-milliseconds">3 min later</div>
          <div class="copy-option" data-minutes="5" data-type="milliseconds" id="five-min-later-milliseconds">5 min later</div>
          <div class="copy-option custom-option" data-type="milliseconds">
            <input type="number" class="custom-minutes-input" id="custom-milliseconds-input" placeholder="Custom" min="1" max="9999">
            <span id="custom-min-later-milliseconds">min later</span>
            <button type="button" class="custom-copy-btn" data-type="milliseconds" id="custom-copy-btn-milliseconds">Copy</button>
          </div>
        </div>
      </div>
    </div>
    <div class="time-row">
      <div class="time-label" id="current-time-label">Current Time</div>
      <div class="time-value" id="current-datetime"></div>
      <div class="copy-btn-container">
        <button class="copy-btn" data-copy="current-datetime" data-type="datetime" id="copy-datetime-btn">📋</button>
        <div class="copy-dropdown" id="copy-dropdown-datetime">
          <div class="copy-option" data-minutes="1" data-type="datetime" id="one-min-later-datetime">1 min later</div>
          <div class="copy-option" data-minutes="3" data-type="datetime" id="three-min-later-datetime">3 min later</div>
          <div class="copy-option" data-minutes="5" data-type="datetime" id="five-min-later-datetime">5 min later</div>
          <div class="copy-option custom-option" data-type="datetime">
            <input type="number" class="custom-minutes-input" id="custom-datetime-input" placeholder="Custom" min="1" max="9999">
            <span id="custom-min-later-datetime">min later</span>
            <button type="button" class="custom-copy-btn" data-type="datetime" id="custom-copy-btn-datetime">Copy</button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="section">
    <h2 class="section-title" id="timestamp-conversion-title">Timestamp Conversion</h2>
    <p class="section-desc" id="timestamp-conversion-desc">Supports seconds, milliseconds, microseconds and nanoseconds</p>

    <input type="text" id="timestamp-input" placeholder="Enter timestamp">
<!--    <script>-->
<!--      document.getElementById('timestamp-input').addEventListener('input', function(event) {-->
<!--        console.log('Input value:', event.target.value);-->
<!--      });-->
<!--    </script>-->

    <table>
      <tr>
        <td id="format-label">Format</td>
        <td id="timestamp-format"></td>
      </tr>
      <tr>
        <td id="gmt-label">GMT</td>
        <td id="timestamp-gmt"></td>
      </tr>
      <tr>
        <td id="local-timezone-label">Local Timezone</td>
        <td id="timestamp-local"></td>
      </tr>
      <tr class="highlight-row">
        <td id="relative-time-label">Relative Time</td>
        <td id="timestamp-relative"></td>
      </tr>
    </table>
  </div>

  <div class="section">
    <h2 class="section-title" id="date-conversion-title">Date Conversion</h2>

    <div class="date-input-container">
      <input type="datetime-local" id="date-input" class="date-picker" step="1">
      <div class="date-input-hint" id="date-input-hint">Select date and time</div>
    </div>

    <table>
      <tr>
        <td id="seconds-label">Timestamp (s)</td>
        <td id="date-timestamp-seconds"></td>
      </tr>
      <tr>
        <td id="milliseconds-label">Timestamp (ms)</td>
        <td id="date-timestamp-milliseconds"></td>
      </tr>
      <tr>
        <td id="date-gmt-label">GMT</td>
        <td id="date-gmt"></td>
      </tr>
      <tr>
        <td id="date-local-label">Local Timezone</td>
        <td id="date-local"></td>
      </tr>
      <tr class="highlight-row">
        <td id="date-relative-label">Relative Time</td>
        <td id="date-relative"></td>
      </tr>
    </table>
  </div>

  <div class="about-section">
    <h2 class="about-title">
      <button id="about-toggle" class="toggle-button" aria-expanded="false" aria-controls="about-content">
        <span>About Unix Timestamp</span>
        <svg class="toggle-icon" viewBox="0 0 24 24" width="16" height="16" aria-hidden="true">
          <path fill="currentColor" d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"></path>
        </svg>
      </button>
    </h2>
    <div id="about-content" class="about-content" hidden>
      <h3 id="what-is-unix-title">What is Unix Timestamp?</h3>
      <p id="what-is-unix-desc">Unix timestamp is an integer representing the number of seconds elapsed since January 1, 1970 00:00:00 UTC (the Unix Epoch).</p>

      <h3 id="time-range-title">Time Range</h3>
      <p id="time-range-start">- Start time: January 1, 1970 00:00:00 UTC, timestamp: 0</p>
      <p id="time-range-end">- End time: January 19, 2038 03:14:07 UTC, timestamp: 2,147,483,647</p>
      <p id="time-range-note">* Note: This limitation is based on 32-bit systems. 64-bit systems can represent ±292,277,026,596 years.</p>

      <h3 id="common-units-title">Common Units</h3>
      <ul>
        <li id="unit-seconds">Seconds: Most commonly used, 10 digits</li>
        <li id="unit-milliseconds">Milliseconds: 1/1000 of a second, 13 digits</li>
        <li id="unit-microseconds">Microseconds: 1/1,000,000 of a second, 16 digits</li>
        <li id="unit-nanoseconds">Nanoseconds: 1/1,000,000,000 of a second, 19 digits</li>
      </ul>

      <h3 id="why-use-title">Why Use Timestamps?</h3>
      <ul>
        <li id="why-use-1">Unified standard: Not affected by time zones</li>
        <li id="why-use-2">Easy calculation: Can be directly compared</li>
        <li id="why-use-3">Storage efficient: Represents complete date and time with a single number</li>
        <li id="why-use-4">Cross-platform: Supported by all mainstream programming languages</li>
      </ul>

      <h3 id="y2038-title">Year 2038 Problem</h3>
      <p id="y2038-desc">On 32-bit systems, Unix timestamp will reach its maximum value of 2,147,483,647 on January 19, 2038 03:14:07 UTC, potentially causing overflow issues. Modern 64-bit systems are not affected by this limitation.</p>
    </div>
  </div>

  <footer class="footer">
    <div class="footer-content">
      <p>© 2025 Unix Timestamp Converter - A useful tool for developers and system administrators.</p>
      <div class="contact-email">
        <a href="mailto:<EMAIL>" class="contact-link">
          <span class="contact-icon">✉️</span> <EMAIL>
          <button class="copy-email-btn" title="Copy email address" data-tooltip="Copy email" data-email="<EMAIL>">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
              <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
            </svg>
          </button>
        </a>
      </div>
    </div>
  </footer>
</div>

<script type="module" src="/script.js"></script>
<script>
  window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };
</script>
<script defer src="/_vercel/insights/script.js"></script>
</body>
</html>